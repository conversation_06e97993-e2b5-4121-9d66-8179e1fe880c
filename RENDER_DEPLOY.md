# 🚀 Render 部署指南

## 📋 部署步骤

### 1. 准备 GitHub 仓库
确保您的代码已推送到 GitHub 仓库。

### 2. 登录 Render
1. 访问 [render.com](https://render.com)
2. 使用 GitHub 账号登录

### 3. 创建新服务
1. 点击 "New +" 按钮
2. 选择 "Web Service"
3. 连接您的 GitHub 仓库
4. 选择 `henurjxylyhsanping` 仓库

### 4. 配置部署设置
- **Name**: `student-assessment-system`
- **Environment**: `Node`
- **Region**: 选择离中国最近的区域（如 Singapore）
- **Branch**: `main` 或 `master`
- **Build Command**: `chmod +x build.sh && ./build.sh && cd server && npm install`
- **Start Command**: `cd server && npm start`

### 5. 环境变量设置
在 Environment Variables 部分添加：

```
NODE_ENV=production
PORT=10000
JWT_SECRET=[自动生成]
ENABLE_RATE_LIMIT=false
ENABLE_SECURITY_CHECKS=false
STRICT_MODE=false
ENABLE_ADMIN_DETAILED_LOGS=false
```

### 6. 部署
点击 "Create Web Service" 开始部署。

## 🎯 部署后访问

部署成功后，您将获得一个类似这样的 URL：
`https://student-assessment-system-xxxx.onrender.com`

## 👤 默认登录账户

- **管理员**: admin / admin123
- **学生**: 学号 / 学号

## 🔧 故障排除

### 构建失败
- 检查 GitHub 仓库是否包含所有必要文件
- 确保 `build.sh` 文件有执行权限

### 启动失败
- 检查环境变量是否正确设置
- 查看 Render 控制台的日志信息

### 访问问题
- 确保使用 HTTPS 访问
- 检查防火墙设置

## 📊 性能优化

### 冷启动优化
- Render 免费层会在 15 分钟无访问后休眠
- 第一次访问需要 30-60 秒启动时间
- 后续访问正常速度

### 数据持久化
- 数据存储在 `server/data.json` 文件中
- 文件上传存储在 `server/uploads/` 目录
- Render 免费层重启后数据会丢失，建议定期备份

## 🌟 使用建议

1. **学生集中使用时间**: 白天课程时间，服务器保持活跃
2. **避免长时间空闲**: 超过 15 分钟无访问会休眠
3. **数据备份**: 定期下载重要数据
4. **监控使用**: 关注 Render 控制台的使用情况

## 🆓 免费额度

Render 免费层提供：
- ✅ 750 小时/月运行时间
- ✅ 512MB RAM
- ✅ 自动 HTTPS
- ✅ 自动部署
- ⚠️ 15 分钟无访问后休眠
- ⚠️ 重启后数据丢失

对于 300 个学生的使用场景完全足够！
