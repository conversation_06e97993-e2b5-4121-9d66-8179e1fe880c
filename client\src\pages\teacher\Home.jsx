import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Typography, 
  Spin, 
  List,
  Tag,
  Progress,
  Alert,
  Button
} from 'antd';
import { 
  UserOutlined, 
  FileTextOutlined, 
  CheckCircleOutlined, 
  ClockCircleOutlined,
  TrophyOutlined,
  TeamOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { userAPI, certificateAPI, scoreAPI } from '../../utils/api';

const { Title, Text } = Typography;

const TeacherHome = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalClasses: 0,
    pendingCertificates: 0,
    approvedCertificates: 0,
    averageScore: 0
  });
  const [recentCertificates, setRecentCertificates] = useState([]);
  const [topStudents, setTopStudents] = useState([]);
  const navigate = useNavigate();

  // 获取当前用户权限
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  const userPermissions = currentUser.permissions || [];
  const isSuperAdmin = currentUser.is_super_admin || false;

  // 权限检查函数
  const hasPermission = (permission) => {
    if (isSuperAdmin) return true;
    return userPermissions.includes(permission);
  };

  // 权限常量
  const PERMISSIONS = {
    CERTIFICATE_AUDIT: 'certificate_audit',
    STUDENT_MANAGEMENT: 'student_management',
    GRADE_EXPORT: 'grade_export',
    ACTIVITY_MANAGEMENT: 'activity_management',
    CATEGORY_MANAGEMENT: 'category_management',
    ADMIN_MANAGEMENT: 'admin_management',
    SYSTEM_SETTINGS: 'system_settings',
    DATA_CLEANUP: 'data_cleanup'
  };

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);

      // 获取学生统计
      let students = [];
      try {
        const studentsResponse = await userAPI.getStudents({ limit: 1000 });
        students = studentsResponse.data.students || [];
      } catch (error) {
        console.error('获取学生数据失败:', error);
      }

      // 获取班级统计
      let classes = [];
      try {
        const classesResponse = await userAPI.getClasses();
        classes = classesResponse.data.classes || [];
      } catch (error) {
        console.error('获取班级数据失败:', error);
      }

      // 获取证书统计（只获取真正待审核的）
      let pendingCerts = [];
      try {
        const certificatesResponse = await certificateAPI.getPendingCertificates({
          limit: 1000,
          status: 'pending' // 只获取待审核状态的证书
        });
        pendingCerts = certificatesResponse.data.certificates || [];
        // 进一步过滤，确保只包含待审核的证书
        pendingCerts = pendingCerts.filter(cert => cert.status === 'pending');
      } catch (error) {
        console.error('获取证书数据失败:', error);
      }

      // 获取已通过证书统计 - 暂时使用模拟数据
      const approvedCerts = [];

      // 获取成绩统计
      let scores = [];
      try {
        const scoresResponse = await scoreAPI.getClassScores({ limit: 1000 });
        scores = scoresResponse.data.scores || [];
      } catch (error) {
        console.error('获取成绩数据失败:', error);
      }

      // 计算平均分
      const averageScore = scores.length > 0
        ? scores.reduce((sum, score) => sum + (score.final_score || 0), 0) / scores.length
        : 0;

      // 获取最近提交的证书
      const recentCerts = pendingCerts.slice(0, 5);

      // 获取成绩前5名学生
      const topStudents = scores
        .sort((a, b) => (b.final_score || 0) - (a.final_score || 0))
        .slice(0, 5);

      setStats({
        totalStudents: students.length,
        totalClasses: classes.length,
        pendingCertificates: pendingCerts.length,
        approvedCertificates: approvedCerts.length,
        averageScore: averageScore
      });

      setRecentCertificates(recentCerts);
      setTopStudents(topStudents);

    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (status) => {
    const statusMap = {
      pending: { color: 'orange', text: '待审核' },
      approved: { color: 'green', text: '已通过' },
      rejected: { color: 'red', text: '已拒绝' }
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }



  return (
    <div style={{ padding: '24px', background: '#fff', borderRadius: '8px', margin: '0', minHeight: '600px' }}>
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ marginBottom: 8, color: '#1890ff' }}>工作台概览</Title>
        <Text type="secondary">欢迎回来，{currentUser.name}！这里是您的工作台概览。</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        {hasPermission(PERMISSIONS.STUDENT_MANAGEMENT) && (
          <>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="学生总数"
                  value={stats.totalStudents}
                  prefix={<TeamOutlined style={{ color: '#1890ff' }} />}
                  valueStyle={{ color: '#1890ff' }}
                  suffix="人"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="班级数量"
                  value={stats.totalClasses}
                  prefix={<UserOutlined style={{ color: '#52c41a' }} />}
                  valueStyle={{ color: '#52c41a' }}
                  suffix="个"
                />
              </Card>
            </Col>
          </>
        )}

        {hasPermission(PERMISSIONS.CERTIFICATE_AUDIT) && (
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="待审核证书"
                value={stats.pendingCertificates}
                prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14' }}
                suffix="个"
              />
            </Card>
          </Col>
        )}

        {hasPermission(PERMISSIONS.GRADE_EXPORT) && (
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="平均成绩"
                value={stats.averageScore}
                precision={2}
                prefix={<TrophyOutlined style={{ color: '#722ed1' }} />}
                valueStyle={{ color: '#722ed1' }}
                suffix="分"
              />
            </Card>
          </Col>
        )}
      </Row>

      {/* 待办事项提醒 */}
      {hasPermission(PERMISSIONS.CERTIFICATE_AUDIT) && stats.pendingCertificates > 0 && (
        <Alert
          message="待办提醒"
          description={
            <div>
              您有 <Text strong>{stats.pendingCertificates}</Text> 个证书待审核，
              <Button 
                type="link" 
                style={{ padding: 0, marginLeft: 4 }}
                onClick={() => navigate('/teacher/certificates')}
              >
                立即处理
              </Button>
            </div>
          }
          type="warning"
          showIcon
          icon={<ExclamationCircleOutlined />}
          style={{ marginBottom: 24 }}
        />
      )}

      <Row gutter={[16, 16]}>
        {/* 最近提交的证书 */}
        {hasPermission(PERMISSIONS.CERTIFICATE_AUDIT) && (
          <Col xs={24} lg={hasPermission(PERMISSIONS.GRADE_EXPORT) ? 12 : 24}>
            <Card
              title="最近提交的证书"
              extra={
                <Button
                  type="link"
                  onClick={() => navigate('/teacher/certificates')}
                >
                  查看全部
                </Button>
              }
            >
            {recentCertificates.length > 0 ? (
              <List
                dataSource={recentCertificates}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      title={
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Text ellipsis style={{ maxWidth: '60%' }}>
                            {item.certificate_name}
                          </Text>
                          {getStatusTag(item.status)}
                        </div>
                      }
                      description={
                        <div>
                          <Text type="secondary">{item.student_name}</Text>
                          <Text type="secondary" style={{ marginLeft: 16 }}>
                            {item.category_name}
                          </Text>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <FileTextOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
                <div style={{ marginTop: 16 }}>
                  <Text type="secondary">暂无证书提交</Text>
                </div>
              </div>
            )}
            </Card>
          </Col>
        )}

        {/* 成绩排行榜 */}
        {hasPermission(PERMISSIONS.GRADE_EXPORT) && (
          <Col xs={24} lg={hasPermission(PERMISSIONS.CERTIFICATE_AUDIT) ? 12 : 24}>
          <Card 
            title="成绩排行榜" 
            extra={
              <Button 
                type="link" 
                onClick={() => navigate('/teacher/scores')}
              >
                查看全部
              </Button>
            }
          >
            {topStudents.length > 0 ? (
              <List
                dataSource={topStudents}
                renderItem={(item, index) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <div style={{
                          width: 32,
                          height: 32,
                          borderRadius: '50%',
                          background: index < 3 ? '#faad14' : '#d9d9d9',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: '#fff',
                          fontWeight: 'bold'
                        }}>
                          {index + 1}
                        </div>
                      }
                      title={
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <div>
                            <Text strong>{item.student_name}</Text>
                            <Text type="secondary" style={{ marginLeft: 8, fontSize: '12px' }}>
                              {item.class_name}
                            </Text>
                          </div>
                          <Text strong style={{ color: '#1890ff' }}>
                            {item.final_score?.toFixed(2)}分
                          </Text>
                        </div>
                      }
                      description={
                        <Progress
                          percent={(item.final_score / 20) * 100}
                          size="small"
                          showInfo={false}
                          strokeColor={index < 3 ? '#faad14' : '#1890ff'}
                        />
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <TrophyOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
                <div style={{ marginTop: 16 }}>
                  <Text type="secondary">暂无成绩数据</Text>
                </div>
              </div>
            )}
            </Card>
          </Col>
        )}
      </Row>

      {/* 权限提示 */}
      {!hasPermission(PERMISSIONS.CERTIFICATE_AUDIT) &&
       !hasPermission(PERMISSIONS.STUDENT_MANAGEMENT) &&
       !hasPermission(PERMISSIONS.GRADE_EXPORT) && (
        <Card style={{ marginTop: 24 }}>
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <UserOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
            <div style={{ marginTop: 16 }}>
              <Text type="secondary">您当前没有分配任何管理权限</Text>
            </div>
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">请联系超级管理员为您分配相应的权限</Text>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default TeacherHome;
