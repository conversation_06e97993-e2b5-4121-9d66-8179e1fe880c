import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  message,
  Space,
  Typography,
  Row,
  Col,
  Select,
  Progress,
  Statistic,
  Tag,
  Descriptions,
  Divider,
  Form,
  InputNumber
} from 'antd';
import {
  TrophyOutlined,
  EyeOutlined,
  ReloadOutlined,
  CalculatorOutlined,
  DownloadOutlined,
  OrderedListOutlined,
  EditOutlined
} from '@ant-design/icons';
import { scoreAPI, userAPI, excelAPI, auditActivityAPI } from '../../utils/api';

const { Title, Text } = Typography;
const { Option } = Select;

const ScoreManagement = () => {
  const [loading, setLoading] = useState(false);
  const [scores, setScores] = useState([]);
  const [classes, setClasses] = useState([]);
  const [selectedClass, setSelectedClass] = useState('');
  const [auditActivities, setAuditActivities] = useState([]);
  const [selectedActivity, setSelectedActivity] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentStudent, setCurrentStudent] = useState(null);
  const [stats, setStats] = useState({
    totalStudents: 0,
    averageScore: 0,
    highestScore: 0,
    passRate: 0
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingStudent, setEditingStudent] = useState(null);
  const [batchEditModalVisible, setBatchEditModalVisible] = useState(false);
  const [selectedStudents, setSelectedStudents] = useState([]);
  const [form] = Form.useForm();
  const [batchForm] = Form.useForm();

  useEffect(() => {
    fetchScores();
    fetchClasses();
    fetchAuditActivities();
  }, []);

  const fetchScores = async (params = {}) => {
    try {
      setLoading(true);
      const response = await scoreAPI.getClassScores({
        page: pagination.current,
        limit: pagination.pageSize,
        class_name: selectedClass,
        audit_activity_id: selectedActivity,
        ...params
      });
      
      const scoresData = response.data.scores || [];
      setScores(scoresData);
      setPagination(prev => ({
        ...prev,
        total: response.data.total || 0
      }));

      // 计算统计数据 - 使用总数据而不是当前页数据
      const totalStudents = response.data.total || 0;
      const allScores = response.data.all_scores || scoresData;

      if (allScores.length > 0) {
        const totalScore = allScores.reduce((sum, score) => sum + (score.final_score || 0), 0);
        const averageScore = totalScore / allScores.length;
        const highestScore = Math.max(...allScores.map(score => score.final_score || 0));
        const passCount = allScores.filter(score => (score.final_score || 0) >= 12).length; // 12分及格
        const passRate = (passCount / allScores.length) * 100;

        setStats({
          totalStudents,
          averageScore,
          highestScore,
          passRate
        });
      } else {
        setStats({
          totalStudents: 0,
          averageScore: 0,
          highestScore: 0,
          passRate: 0
        });
      }
    } catch (error) {
      console.error('获取成绩列表失败:', error);
      message.error('获取成绩列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchClasses = async () => {
    try {
      const response = await userAPI.getClasses();
      setClasses(response.data.classes || []);
    } catch (error) {
      console.error('获取班级列表失败:', error);
    }
  };

  const fetchAuditActivities = async () => {
    try {
      const response = await auditActivityAPI.getActivities();
      setAuditActivities(response.data.activities || []);
    } catch (error) {
      console.error('获取审核活动失败:', error);
    }
  };

  const handleRecalculate = async () => {
    try {
      setLoading(true);
      await scoreAPI.recalculateScores();
      message.success('成绩重新计算完成');
      fetchScores();
    } catch (error) {
      console.error('重新计算失败:', error);
      message.error('重新计算失败');
    } finally {
      setLoading(false);
    }
  };

  // 编辑学生成绩
  const handleEditScore = (student) => {
    setEditingStudent(student);
    form.setFieldsValue({
      base_score: student.base_score || 25,
      professional_score: student.professional_score || 0,
      sports_arts_score: student.sports_arts_score || 0,
      moral_score: student.moral_score || 0
    });
    setEditModalVisible(true);
  };

  // 保存成绩修改
  const handleSaveScore = async (values) => {
    try {
      setLoading(true);
      await scoreAPI.updateStudentScore(editingStudent.user_id, values);
      message.success('成绩修改成功');
      setEditModalVisible(false);
      form.resetFields();
      setEditingStudent(null);
      fetchScores();
    } catch (error) {
      console.error('修改成绩失败:', error);
      message.error('修改成绩失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 批量编辑成绩
  const handleBatchEdit = () => {
    if (selectedStudents.length === 0) {
      message.warning('请先选择要修改的学生');
      return;
    }
    setBatchEditModalVisible(true);
  };

  // 批量保存成绩
  const handleBatchSaveScore = async (values) => {
    try {
      setLoading(true);
      await scoreAPI.batchUpdateScores({
        student_ids: selectedStudents,
        ...values
      });
      message.success(`成功修改 ${selectedStudents.length} 个学生的成绩`);
      setBatchEditModalVisible(false);
      batchForm.resetFields();
      setSelectedStudents([]);
      fetchScores();
    } catch (error) {
      console.error('批量修改成绩失败:', error);
      message.error('批量修改成绩失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  const handleExportScores = async () => {
    try {
      const response = await excelAPI.exportScores({
        class_name: selectedClass,
        audit_activity_id: selectedActivity
      });
      
      const blob = new Blob([response], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `成绩报表_${selectedClass || '全部'}_${new Date().toLocaleDateString()}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败');
    }
  };

  const handleViewDetail = async (record) => {
    try {
      const response = await scoreAPI.getStudentScore(record.user_id);
      setCurrentStudent({
        ...record,
        certificates: response.data.certificates || []
      });
      setDetailModalVisible(true);
    } catch (error) {
      console.error('获取学生详情失败:', error);
      message.error('获取学生详情失败');
    }
  };

  const handleClassFilter = (value) => {
    setSelectedClass(value);
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchScores({ class_name: value, page: 1 });
  };

  const handleTableChange = (paginationInfo) => {
    setPagination(paginationInfo);
    fetchScores({
      page: paginationInfo.current,
      limit: paginationInfo.pageSize
    });
  };

  const getRankColor = (index) => {
    if (index === 0) return '#faad14'; // 金色
    if (index === 1) return '#d9d9d9'; // 银色
    if (index === 2) return '#cd7f32'; // 铜色
    return '#1890ff';
  };

  const columns = [
    {
      title: '排名',
      key: 'rank',
      width: 80,
      render: (_, record, index) => (
        <div style={{
          width: 32,
          height: 32,
          borderRadius: '50%',
          background: getRankColor(index),
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#fff',
          fontWeight: 'bold'
        }}>
          {index + 1}
        </div>
      ),
    },
    {
      title: '学生信息',
      key: 'student_info',
      width: 150,
      render: (_, record) => (
        <div>
          <Text strong>{record.student_name}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.student_id}
          </Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.class_name}
          </Text>
        </div>
      ),
    },
    {
      title: '最终成绩',
      dataIndex: 'final',
      key: 'final',
      width: 120,
      sorter: (a, b) => (a.final || 0) - (b.final || 0),
      render: (score) => (
        <div>
          <Text strong style={{ color: '#1890ff', fontSize: '16px' }}>
            {score ? score.toFixed(2) : '0.00'}分
          </Text>
          <br />
          <Progress
            percent={(score / 20) * 100}
            size="small"
            showInfo={false}
            strokeColor="#1890ff"
          />
        </div>
      ),
    },
    {
      title: '总分',
      dataIndex: 'total',
      key: 'total',
      width: 100,
      render: (score) => (
        <Text strong>{score ? score.toFixed(2) : '0.00'}分</Text>
      ),
    },
    {
      title: '专业类详细',
      key: 'professional_detail',
      width: 150,
      render: (_, record) => (
        <div style={{ fontSize: '12px' }}>
          <div>学科竞赛: {record.academic_competition || 0}分</div>
          <div>实践技能: {record.practical_skills || 0}分</div>
          <div>创新项目: {record.innovation_project || 0}分</div>
          <div>专业认证: {record.professional_cert || 0}分</div>
          <div>科学研究: {record.research || 0}分</div>
          <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
            小计: {record.professional_total || 0}分
          </div>
        </div>
      ),
    },
    {
      title: '体育美育详细',
      key: 'sports_arts_detail',
      width: 120,
      render: (_, record) => (
        <div style={{ fontSize: '12px' }}>
          <div>体育竞赛: {record.sports_competition || 0}分</div>
          <div>文艺活动: {record.arts_activity || 0}分</div>
          <div>英语六级: {record.english_cet6 || 0}分</div>
          <div style={{ fontWeight: 'bold', color: '#722ed1' }}>
            小计: {record.sports_arts_total || 0}分
          </div>
        </div>
      ),
    },
    {
      title: '文明品德详细',
      key: 'moral_detail',
      width: 120,
      render: (_, record) => (
        <div style={{ fontSize: '12px' }}>
          <div>学生干部: {record.student_leader || 0}分</div>
          <div>志愿服务: {record.volunteer_service || 0}分</div>
          <div>新闻采编: {record.news_writing || 0}分</div>
          <div>道德表彰: {record.moral_honor || 0}分</div>
          <div style={{ fontWeight: 'bold', color: '#fa8c16' }}>
            小计: {record.moral_total || 0}分
          </div>
        </div>
      ),
    },
    {
      title: '证书数量',
      key: 'certificate_count',
      width: 100,
      render: (_, record) => (
        <Tag color="blue">
          {record.certificate_count || 0}个
        </Tag>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 120,
      render: (time) => time ? new Date(time).toLocaleDateString() : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditScore(record)}
          >
            修改
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={2}>
            <TrophyOutlined /> 成绩管理
          </Title>
        </Col>
        <Col>
          <Space>
            <Button
              icon={<EditOutlined />}
              onClick={handleBatchEdit}
              disabled={selectedStudents.length === 0}
            >
              批量修改 ({selectedStudents.length})
            </Button>
            <Button
              icon={<CalculatorOutlined />}
              onClick={handleRecalculate}
              loading={loading}
            >
              重新计算
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExportScores}
            >
              导出成绩
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 筛选区域 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <div style={{ marginBottom: 8 }}>
              <Text strong>选择审核活动：</Text>
            </div>
            <Select
              style={{ width: '100%' }}
              placeholder="请选择审核活动"
              value={selectedActivity}
              onChange={(value) => {
                setSelectedActivity(value);
                setPagination(prev => ({ ...prev, current: 1 }));
                fetchScores({ audit_activity_id: value });
              }}
              allowClear
            >
              {auditActivities.map(activity => (
                <Option key={activity.id} value={activity.id}>
                  {activity.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={8}>
            <div style={{ marginBottom: 8 }}>
              <Text strong>选择班级：</Text>
            </div>
            <Select
              style={{ width: '100%' }}
              placeholder="选择班级"
              value={selectedClass}
              onChange={(value) => {
                setSelectedClass(value);
                setPagination(prev => ({ ...prev, current: 1 }));
                fetchScores({ class_name: value });
              }}
              allowClear
            >
              {classes.map(cls => (
                <Option key={cls.class_name} value={cls.class_name}>
                  {cls.class_name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={8}>
            {selectedActivity && (
              <div style={{ padding: '8px 12px', backgroundColor: '#f0f9ff', border: '1px solid #91d5ff', borderRadius: '6px' }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>当前活动：</Text>
                <br />
                <Text strong style={{ fontSize: '14px' }}>
                  {auditActivities.find(a => a.id === selectedActivity)?.name}
                </Text>
              </div>
            )}
          </Col>
        </Row>
      </Card>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="学生总数"
              value={stats.totalStudents}
              suffix="人"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均成绩"
              value={stats.averageScore}
              precision={2}
              suffix="分"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="最高成绩"
              value={stats.highestScore}
              precision={2}
              suffix="分"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="及格率"
              value={stats.passRate}
              precision={1}
              suffix="%"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 筛选 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8} md={6}>
            <Select
              placeholder="选择班级"
              allowClear
              style={{ width: '100%' }}
              onChange={handleClassFilter}
              value={selectedClass || undefined}
            >
              {classes.map(cls => (
                <Option key={cls.class_name} value={cls.class_name}>
                  {cls.class_name} ({cls.student_count}人)
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Button 
              icon={<ReloadOutlined />}
              onClick={() => {
                setSelectedClass('');
                fetchScores({ class_name: '', page: 1 });
              }}
            >
              重置
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 成绩排行榜 */}
      <Card title={<><OrderedListOutlined /> 成绩排行榜</>}>
        <Table
          columns={columns}
          dataSource={scores.sort((a, b) => (b.final_score || 0) - (a.final_score || 0))}
          rowKey="user_id"
          loading={loading}
          rowSelection={{
            selectedRowKeys: selectedStudents,
            onChange: (selectedRowKeys) => {
              setSelectedStudents(selectedRowKeys);
            },
            getCheckboxProps: (record) => ({
              name: record.student_name,
            }),
          }}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 学生详情模态框 */}
      <Modal
        title="学生成绩详情"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setCurrentStudent(null);
        }}
        footer={null}
        width={800}
      >
        {currentStudent && (
          <div>
            <Descriptions title="基本信息" bordered column={2}>
              <Descriptions.Item label="姓名">{currentStudent.student_name}</Descriptions.Item>
              <Descriptions.Item label="学号">{currentStudent.student_id}</Descriptions.Item>
              <Descriptions.Item label="班级">{currentStudent.class_name}</Descriptions.Item>
              <Descriptions.Item label="最终成绩">
                <Text strong style={{ color: '#1890ff', fontSize: '16px' }}>
                  {currentStudent.final_score?.toFixed(2)}分
                </Text>
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Title level={4}>成绩构成</Title>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card size="small" title="基础分值">
                  <Progress
                    percent={(currentStudent.base_score / 25) * 100}
                    format={() => `${currentStudent.base_score}/25`}
                    strokeColor="#52c41a"
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card size="small" title="专业类活动分值">
                  <Progress
                    percent={(currentStudent.professional_score / 30) * 100}
                    format={() => `${currentStudent.professional_score}/30`}
                    strokeColor="#1890ff"
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card size="small" title="体育美育综合活动分值">
                  <Progress
                    percent={(currentStudent.sports_arts_score / 20) * 100}
                    format={() => `${currentStudent.sports_arts_score}/20`}
                    strokeColor="#722ed1"
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card size="small" title="文明品德综合活动分值">
                  <Progress
                    percent={(currentStudent.moral_score / 25) * 100}
                    format={() => `${currentStudent.moral_score}/25`}
                    strokeColor="#fa8c16"
                  />
                </Card>
              </Col>
            </Row>

            {currentStudent.certificates && currentStudent.certificates.length > 0 && (
              <>
                <Divider />
                <Title level={4}>获得证书</Title>
                <Table
                  size="small"
                  dataSource={currentStudent.certificates}
                  rowKey="id"
                  pagination={false}
                  columns={[
                    {
                      title: '证书名称',
                      dataIndex: 'certificate_name',
                      key: 'certificate_name',
                    },
                    {
                      title: '类别',
                      dataIndex: 'category_name',
                      key: 'category_name',
                    },
                    {
                      title: '得分',
                      dataIndex: 'score',
                      key: 'score',
                      render: (score) => `${score}分`,
                    },
                  ]}
                />
              </>
            )}
          </div>
        )}
      </Modal>

      {/* 成绩编辑Modal */}
      <Modal
        title="修改学生成绩"
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          form.resetFields();
          setEditingStudent(null);
        }}
        footer={null}
        width={600}
      >
        {editingStudent && (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSaveScore}
          >
            <div style={{ marginBottom: 16 }}>
              <Text strong>学生信息：</Text>
              <Text>{editingStudent.student_name} ({editingStudent.student_id})</Text>
              <br />
              <Text type="secondary">{editingStudent.class_name}</Text>
            </div>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="基础分值"
                  name="base_score"
                  rules={[
                    { required: true, message: '请输入基础分值' },
                    { type: 'number', min: 0, max: 25, message: '基础分值范围0-25分' }
                  ]}
                >
                  <InputNumber
                    min={0}
                    max={25}
                    step={0.1}
                    style={{ width: '100%' }}
                    addonAfter="分"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="专业类活动分值"
                  name="professional_score"
                  rules={[
                    { required: true, message: '请输入专业类活动分值' },
                    { type: 'number', min: 0, max: 30, message: '专业类活动分值范围0-30分' }
                  ]}
                >
                  <InputNumber
                    min={0}
                    max={30}
                    step={0.1}
                    style={{ width: '100%' }}
                    addonAfter="分"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="体育美育分值"
                  name="sports_arts_score"
                  rules={[
                    { required: true, message: '请输入体育美育分值' },
                    { type: 'number', min: 0, max: 20, message: '体育美育分值范围0-20分' }
                  ]}
                >
                  <InputNumber
                    min={0}
                    max={20}
                    step={0.1}
                    style={{ width: '100%' }}
                    addonAfter="分"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="文明品德分值"
                  name="moral_score"
                  rules={[
                    { required: true, message: '请输入文明品德分值' },
                    { type: 'number', min: 0, max: 25, message: '文明品德分值范围0-25分' }
                  ]}
                >
                  <InputNumber
                    min={0}
                    max={25}
                    step={0.1}
                    style={{ width: '100%' }}
                    addonAfter="分"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Divider />

            <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
              <Space>
                <Button onClick={() => setEditModalVisible(false)}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit" loading={loading}>
                  保存修改
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>

      {/* 批量编辑Modal */}
      <Modal
        title={`批量修改成绩 (${selectedStudents.length}个学生)`}
        open={batchEditModalVisible}
        onCancel={() => {
          setBatchEditModalVisible(false);
          batchForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={batchForm}
          layout="vertical"
          onFinish={handleBatchSaveScore}
        >
          <div style={{ marginBottom: 16 }}>
            <Text type="secondary">
              将对选中的 {selectedStudents.length} 个学生进行批量修改。只填写需要修改的项目，空白项目将保持原值不变。
            </Text>
          </div>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="基础分值调整"
                name="base_score_adjustment"
                extra="输入正数增加分值，负数减少分值"
              >
                <InputNumber
                  min={-25}
                  max={25}
                  step={0.1}
                  style={{ width: '100%' }}
                  addonAfter="分"
                  placeholder="如: +5 或 -2"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="专业类活动分值调整"
                name="professional_score_adjustment"
                extra="输入正数增加分值，负数减少分值"
              >
                <InputNumber
                  min={-30}
                  max={30}
                  step={0.1}
                  style={{ width: '100%' }}
                  addonAfter="分"
                  placeholder="如: +5 或 -2"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="体育美育分值调整"
                name="sports_arts_score_adjustment"
                extra="输入正数增加分值，负数减少分值"
              >
                <InputNumber
                  min={-20}
                  max={20}
                  step={0.1}
                  style={{ width: '100%' }}
                  addonAfter="分"
                  placeholder="如: +5 或 -2"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="文明品德分值调整"
                name="moral_score_adjustment"
                extra="输入正数增加分值，负数减少分值"
              >
                <InputNumber
                  min={-25}
                  max={25}
                  step={0.1}
                  style={{ width: '100%' }}
                  addonAfter="分"
                  placeholder="如: +5 或 -2"
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setBatchEditModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                批量修改
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ScoreManagement;
