const { handleCors, handleSuccess } = require('./_middleware');

module.exports = async (req, res) => {
  // 处理 CORS
  if (handleCors(req, res)) return;

  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: '方法不允许' });
  }

  try {
    const categories = [
      { id: 1, name: '英语等级证书', description: '大学英语四六级、专业英语等级证书' },
      { id: 2, name: '计算机等级证书', description: '全国计算机等级考试证书' },
      { id: 3, name: '专业技能证书', description: '各类专业技能认证证书' },
      { id: 4, name: '学科竞赛获奖', description: '各类学科竞赛获奖证书' },
      { id: 5, name: '文体活动获奖', description: '文艺体育活动获奖证书' },
      { id: 6, name: '社会实践证明', description: '社会实践活动证明材料' },
      { id: 7, name: '志愿服务证明', description: '志愿服务活动证明材料' },
      { id: 8, name: '学生干部证明', description: '学生干部任职证明材料' }
    ];

    handleSuccess(res, categories, '获取证书类别成功');

  } catch (error) {
    handleError(res, error);
  }
};
