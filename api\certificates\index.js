import {
  db,
  authenticateToken,
  handleCors,
  handleError,
  handleSuccess
} from '../_middleware.js';

export default async function handler(req, res) {
  // 处理 CORS
  if (handleCors(req, res)) return;

  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: '方法不允许' });
  }

  try {
    // 验证令牌
    const { user } = authenticateToken(req);
    
    // 只允许学生查看自己的证书
    if (user.role !== 'student') {
      return handleError(res, new Error('只有学生可以查看证书'), 403);
    }

    const userId = user.id;
    const { page = 1, limit = 10, status = '', category_id = '', audit_activity_id = '' } = req.query;

    // 构建过滤条件
    const filters = { user_id: userId };
    if (status) filters.status = status;
    if (audit_activity_id) filters.audit_activity_id = parseInt(audit_activity_id);

    // 获取用户的证书
    let userCertificates = await db.getCertificates(filters);

    // 类别过滤（在内存中进行，因为 Supabase 查询已经处理了其他过滤）
    if (category_id) {
      userCertificates = userCertificates.filter(cert => cert.category_id == category_id);
    }

    // 分页
    const offset = (page - 1) * limit;
    const paginatedCertificates = userCertificates.slice(offset, offset + parseInt(limit));

    // 添加用户信息
    const users = await db.getUsers();
    const certificatesWithUser = paginatedCertificates.map(cert => {
      const certUser = users.find(u => u.id === cert.user_id);
      return {
        ...cert,
        user_name: certUser?.name || '未知用户',
        student_id: certUser?.student_id || certUser?.username || '未知学号',
        class_name: certUser?.class_name || '未知班级'
      };
    });

    handleSuccess(res, {
      certificates: certificatesWithUser,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: userCertificates.length,
        total_pages: Math.ceil(userCertificates.length / limit)
      }
    }, '获取证书列表成功');

  } catch (error) {
    handleError(res, error, 401);
  }
}
