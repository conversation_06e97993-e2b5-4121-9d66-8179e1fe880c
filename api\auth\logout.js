const { 
  tokenBlacklist, 
  authenticateToken, 
  handleCors, 
  handleError, 
  handleSuccess,
  logOperation 
} = require('../_middleware');

module.exports = async (req, res) => {
  // 处理 CORS
  if (handleCors(req, res)) return;

  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: '方法不允许' });
  }

  try {
    // 验证令牌
    const { user, token } = authenticateToken(req);
    
    // 将令牌加入黑名单
    tokenBlacklist.add(token);
    
    // 记录登出日志
    logOperation(user.id, 'logout', {
      logout_method: 'manual'
    });

    handleSuccess(res, null, '登出成功');

  } catch (error) {
    handleError(res, error, 401);
  }
};
