import {
  db,
  authenticateToken,
  handleCors,
  handleError,
  handleSuccess
} from '../_middleware.js';

export default async function handler(req, res) {
  // 处理 CORS
  if (handleCors(req, res)) return;

  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: '方法不允许' });
  }

  try {
    // 验证令牌
    const { user: tokenUser } = authenticateToken(req);

    // 获取最新用户信息
    const user = await db.getUserById(tokenUser.id);

    if (!user) {
      return handleError(res, new Error('用户不存在'), 404);
    }

    const userInfo = {
      id: user.id,
      username: user.username,
      name: user.name,
      role: user.role,
      class_name: user.class_name,
      is_super_admin: user.is_super_admin || false,
      permissions: user.permissions || []
    };

    handleSuccess(res, userInfo, '获取用户信息成功');

  } catch (error) {
    handleError(res, error, 401);
  }
}
