import axios from 'axios';
import { message } from 'antd';

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    const { response } = error;
    
    if (response) {
      const { status, data } = response;
      
      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/login';
          message.error(data.message || '登录已过期，请重新登录');
          break;
        case 403:
          message.error(data.message || '没有权限访问');
          break;
        case 404:
          message.error(data.message || '请求的资源不存在');
          break;
        case 500:
          message.error(data.message || '服务器内部错误');
          break;
        default:
          message.error(data.message || '请求失败');
      }
    } else {
      message.error('网络错误，请检查网络连接');
    }
    
    return Promise.reject(error);
  }
);

// 认证相关API
export const authAPI = {
  // 登录
  login: (data) => api.post('/auth/login', data),
  
  // 获取当前用户信息
  getCurrentUser: () => api.get('/auth/me'),
  
  // 修改密码
  changePassword: (data) => api.put('/auth/change-password', data),
  
  // 验证token
  verifyToken: () => api.post('/auth/verify'),
};

// 用户管理API
export const userAPI = {
  // 获取学生列表
  getStudents: (params) => api.get('/users/students', { params }),
  
  // 批量创建学生
  batchCreateStudents: (data) => api.post('/users/batch-create', data),
  
  // 删除学生
  deleteStudent: (id) => api.delete(`/users/students/${id}`),

  // 批量删除学生
  batchDeleteStudents: (data) => api.post('/users/students/batch-delete', data),

  // 清空所有学生
  clearAllStudents: () => api.post('/users/students/clear-all'),

  // 添加学生
  createStudent: (data) => api.post('/users/students', data),

  // 修改学生信息
  updateStudent: (id, data) => api.put(`/users/students/${id}`, data),

  // 重置学生密码
  resetStudentPassword: (id) => api.put(`/users/students/${id}/reset-password`),
  
  // 获取班级列表
  getClasses: () => api.get('/users/classes'),

  // 管理员管理
  getAdministrators: () => api.get('/users/administrators'),
  createAdministrator: (data) => api.post('/users/administrators', data),
  updateAdministrator: (id, data) => api.put(`/users/administrators/${id}`, data),
  deleteAdministrator: (id) => api.delete(`/users/administrators/${id}`),
  resetAdministratorPassword: (id) => api.post(`/users/administrators/${id}/reset-password`),
};

// 证书管理API
export const certificateAPI = {
  // 提交证书
  submitCertificate: (formData) => {
    return api.post('/certificates', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 批量提交证书
  batchSubmitCertificates: (formData) => {
    return api.post('/certificates/batch', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // 获取我的证书
  getMyCertificates: (params) => api.get('/certificates/my', { params }),

  // 删除证书
  deleteCertificate: (id) => api.delete(`/certificates/${id}`),

  // 获取待审核证书
  getPendingCertificates: (params) => api.get('/certificates/pending', { params }),

  // 审核证书
  auditCertificate: (id, data) => api.put(`/certificates/${id}/audit`, data),

  // 获取审核历史
  getAuditHistory: (id) => api.get(`/certificates/${id}/audit-history`),

  // 更新证书
  updateCertificate: (id, data) => {
    return api.put(`/certificates/${id}`, data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 删除证书
  deleteCertificate: (id) => api.delete(`/certificates/${id}`),

  // 批量删除证书
  batchDeleteCertificates: (data) => api.post('/certificates/batch-delete', data),
};

// 审核活动管理API
export const auditActivityAPI = {
  // 获取审核活动列表
  getActivities: () => api.get('/audit-activities'),

  // 创建审核活动
  createActivity: (data) => api.post('/audit-activities', data),

  // 更新审核活动
  updateActivity: (id, data) => api.put(`/audit-activities/${id}`, data),

  // 删除审核活动
  deleteActivity: (id) => api.delete(`/audit-activities/${id}`),
};

// 管理员工具API
export const adminAPI = {
  // 清理无效证书
  cleanupCertificates: () => api.post('/admin/cleanup-certificates'),

  // 管理员管理
  getAdministrators: () => api.get('/administrators'),
  createAdministrator: (data) => api.post('/administrators', data),
  updateAdministrator: (id, data) => api.put(`/administrators/${id}`, data),
  deleteAdministrator: (id) => api.delete(`/administrators/${id}`),
  resetAdministratorPassword: (id, data) => api.post(`/administrators/${id}/reset-password`, data),

  // 操作日志
  getOperationLogs: (params) => api.get('/operation-logs', { params }),
  deleteOperationLog: (id) => api.delete(`/operation-logs/${id}`),
  batchDeleteOperationLogs: (data) => api.post('/operation-logs/batch-delete', data),
  clearOperationLogs: () => api.post('/operation-logs/clear'),

  // 权限管理
  getPermissions: () => api.get('/permissions'),
};

// 类别管理API
export const categoryAPI = {
  // 获取所有类别
  getCategories: () => api.get('/categories'),
  
  // 获取主要类别
  getMainCategories: () => api.get('/categories/main'),
  
  // 获取类别详情
  getCategoryDetail: (id) => api.get(`/categories/${id}`),
  
  // 获取类别统计
  getCategoryStats: () => api.get('/categories/stats'),
};

// 成绩管理API
export const scoreAPI = {
  // 获取我的成绩
  getMyScore: (params) => api.get('/scores/my', { params }),

  // 获取班级成绩
  getClassScores: (params) => api.get('/scores/class', { params }),

  // 获取学生详细成绩
  getStudentScore: (id) => api.get(`/scores/student/${id}`),

  // 重新计算成绩
  recalculateScores: () => api.post('/scores/recalculate'),

  // 修改学生成绩
  updateStudentScore: (id, data) => api.put(`/scores/student/${id}`, data),

  // 批量修改成绩
  batchUpdateScores: (data) => api.put('/scores/batch', data),
};

// 系统设置API
export const settingsAPI = {
  // 获取系统设置
  getSettings: () => api.get('/settings'),

  // 更新系统设置
  updateSettings: (data) => api.put('/settings', data),
};

// Excel相关API
export const excelAPI = {
  // 批量导入学生
  importStudents: (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post('/excel/import-students', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // 导出成绩
  exportScores: (params) => {
    return api.get('/excel/export-scores', {
      params,
      responseType: 'blob',
    });
  },
  
  // 下载学生模板
  downloadStudentTemplate: () => {
    return api.get('/excel/student-template', {
      responseType: 'blob',
    });
  },
};

// 工具函数
export const downloadFile = (blob, filename) => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};





export default api;
