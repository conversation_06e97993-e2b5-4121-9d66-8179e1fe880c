import {
  db,
  authenticateToken,
  requireTeacher,
  handleCors,
  handleError,
  handleSuccess,
  logOperation
} from '../_middleware.js';

export default async function handler(req, res) {
  // 处理 CORS
  if (handleCors(req, res)) return;

  try {
    // 验证令牌
    const { user } = authenticateToken(req);

    if (req.method === 'GET') {
      // 获取学生列表
      requireTeacher(user);

      const { page = 1, limit = 10, search = '', class_name = '' } = req.query;

      // 构建过滤条件
      const filters = { role: 'student' };
      
      // 获取所有学生
      let students = await db.getUsers(filters);

      // 搜索过滤
      if (search) {
        const searchLower = search.toLowerCase();
        students = students.filter(student => 
          student.name?.toLowerCase().includes(searchLower) ||
          student.username?.toLowerCase().includes(searchLower) ||
          student.student_id?.toLowerCase().includes(searchLower)
        );
      }

      // 班级过滤
      if (class_name) {
        students = students.filter(student => student.class_name === class_name);
      }

      // 排序
      students.sort((a, b) => {
        if (a.class_name !== b.class_name) {
          return (a.class_name || '').localeCompare(b.class_name || '');
        }
        return (a.name || '').localeCompare(b.name || '');
      });

      // 分页
      const offset = (page - 1) * limit;
      const paginatedStudents = students.slice(offset, offset + parseInt(limit));

      // 移除敏感信息
      const safeStudents = paginatedStudents.map(student => ({
        id: student.id,
        username: student.username,
        student_id: student.student_id,
        name: student.name,
        class_name: student.class_name,
        created_at: student.created_at
      }));

      handleSuccess(res, {
        students: safeStudents,
        pagination: {
          current_page: parseInt(page),
          per_page: parseInt(limit),
          total: students.length,
          total_pages: Math.ceil(students.length / limit)
        }
      }, '获取学生列表成功');

    } else if (req.method === 'POST') {
      // 添加学生
      requireTeacher(user);

      const { student_id, name, class_name, password } = req.body;

      if (!student_id || !name || !password) {
        return handleError(res, new Error('学号、姓名和密码不能为空'), 400);
      }

      // 检查学号是否已存在
      const existingUsers = await db.getUsers({ student_id });
      if (existingUsers.length > 0) {
        return handleError(res, new Error('学号已存在'), 400);
      }

      // 检查用户名是否已存在
      const existingUsernames = await db.getUsers({ username: student_id });
      if (existingUsernames.length > 0) {
        return handleError(res, new Error('用户名已存在'), 400);
      }

      // 创建新学生
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash(password, 10);

      const newStudent = await db.createUser({
        username: student_id,
        student_id,
        name,
        class_name: class_name || null,
        password: hashedPassword,
        role: 'student'
      });

      // 记录操作日志
      await logOperation(user.id, 'create_student', {
        student_id: newStudent.id,
        student_name: newStudent.name,
        student_username: newStudent.username
      });

      // 返回安全的用户信息
      const safeStudent = {
        id: newStudent.id,
        username: newStudent.username,
        student_id: newStudent.student_id,
        name: newStudent.name,
        class_name: newStudent.class_name,
        created_at: newStudent.created_at
      };

      handleSuccess(res, safeStudent, '添加学生成功');

    } else {
      return res.status(405).json({ success: false, message: '方法不允许' });
    }

  } catch (error) {
    handleError(res, error, 401);
  }
}
