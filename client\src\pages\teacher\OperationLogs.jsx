import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Select,
  DatePicker,
  Input,
  Space,
  Tag,
  Row,
  Col,
  Typography,
  Button,
  Tooltip,
  Descriptions,
  Popconfirm,
  Modal,
  message,
  Checkbox
} from 'antd';
import {
  HistoryOutlined,
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  DeleteOutlined,
  ClearOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { adminAPI } from '../../utils/api';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Search } = Input;

const OperationLogs = () => {
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState([]);
  const [administrators, setAdministrators] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [batchDeleteModalVisible, setBatchDeleteModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentLogDetail, setCurrentLogDetail] = useState(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [filters, setFilters] = useState({
    user_id: '',
    action: '',
    start_date: '',
    end_date: ''
  });

  useEffect(() => {
    fetchLogs();
    fetchAdministrators();
  }, []);

  const fetchLogs = async (params = {}) => {
    try {
      setLoading(true);
      const response = await adminAPI.getOperationLogs({
        page: pagination.current,
        limit: pagination.pageSize,
        ...filters,
        ...params
      });

      setLogs(response.data.logs);
      setPagination(prev => ({
        ...prev,
        total: response.data.total,
        current: response.data.page
      }));
    } catch (error) {
      console.error('获取操作日志失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAdministrators = async () => {
    try {
      const response = await adminAPI.getAdministrators();
      setAdministrators(response.data);
    } catch (error) {
      console.error('获取管理员列表失败:', error);
    }
  };

  const handleTableChange = (newPagination) => {
    setPagination(newPagination);
    fetchLogs({
      page: newPagination.current,
      limit: newPagination.pageSize
    });
  };

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    
    // 重置到第一页
    setPagination(prev => ({ ...prev, current: 1 }));
    
    fetchLogs({
      ...newFilters,
      page: 1,
      limit: pagination.pageSize
    });
  };

  const handleDateRangeChange = (dates) => {
    const newFilters = {
      ...filters,
      start_date: dates?.[0]?.format('YYYY-MM-DD') || '',
      end_date: dates?.[1]?.format('YYYY-MM-DD') || ''
    };
    setFilters(newFilters);

    setPagination(prev => ({ ...prev, current: 1 }));
    fetchLogs({
      ...newFilters,
      page: 1,
      limit: pagination.pageSize
    });
  };

  // 删除单个日志
  const handleDeleteLog = async (id) => {
    try {
      await adminAPI.deleteOperationLog(id);
      message.success('操作日志删除成功');
      fetchLogs();
      setSelectedRowKeys(selectedRowKeys.filter(key => key !== id));
    } catch (error) {
      console.error('删除操作日志失败:', error);
      message.error(error.response?.data?.message || '删除操作日志失败');
    }
  };

  // 批量删除选中的日志
  const handleBatchDeleteSelected = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的日志');
      return;
    }

    try {
      await adminAPI.batchDeleteOperationLogs({
        delete_type: 'selected',
        log_ids: selectedRowKeys
      });
      message.success(`成功删除 ${selectedRowKeys.length} 条操作日志`);
      setSelectedRowKeys([]);
      fetchLogs();
    } catch (error) {
      console.error('批量删除失败:', error);
      message.error(error.response?.data?.message || '批量删除失败');
    }
  };

  // 删除指定日期之前的日志
  const handleDeleteBeforeDate = async (beforeDate) => {
    try {
      await adminAPI.batchDeleteOperationLogs({
        delete_type: 'before_date',
        before_date: beforeDate
      });
      message.success('删除成功');
      setBatchDeleteModalVisible(false);
      fetchLogs();
    } catch (error) {
      console.error('删除失败:', error);
      message.error(error.response?.data?.message || '删除失败');
    }
  };

  // 清空所有日志
  const handleClearAllLogs = async () => {
    try {
      await adminAPI.clearOperationLogs();
      message.success('所有操作日志已清空');
      setSelectedRowKeys([]);
      fetchLogs();
    } catch (error) {
      console.error('清空日志失败:', error);
      message.error(error.response?.data?.message || '清空日志失败');
    }
  };

  const getActionTag = (action) => {
    const actionColors = {
      '登录': 'green',
      '登出': 'orange',
      '创建': 'blue',
      '更新': 'cyan',
      '删除': 'red',
      '审核': 'purple',
      '导出': 'gold',
      '清理': 'magenta'
    };

    for (const [key, color] of Object.entries(actionColors)) {
      if (action.includes(key)) {
        return <Tag color={color}>{action}</Tag>;
      }
    }
    
    return <Tag>{action}</Tag>;
  };

  const formatDetails = (details) => {
    if (!details || typeof details !== 'object') return '-';

    return (
      <Button
        type="link"
        size="small"
        icon={<EyeOutlined />}
        onClick={() => {
          setCurrentLogDetail(details);
          setDetailModalVisible(true);
        }}
      >
        查看详情
      </Button>
    );
  };

  const renderDetailModal = () => {
    if (!currentLogDetail) return null;

    // 过滤敏感信息
    const filteredDetails = {};
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];

    Object.entries(currentLogDetail).forEach(([key, value]) => {
      // 检查是否为敏感字段
      const isSensitive = sensitiveFields.some(field =>
        key.toLowerCase().includes(field.toLowerCase())
      );

      if (isSensitive) {
        filteredDetails[key] = '***'; // 隐藏敏感信息
      } else {
        filteredDetails[key] = value;
      }
    });

    return (
      <Modal
        title="操作详情"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setCurrentLogDetail(null);
        }}
        footer={[
          <Button
            key="close"
            onClick={() => {
              setDetailModalVisible(false);
              setCurrentLogDetail(null);
            }}
          >
            关闭
          </Button>
        ]}
        width={600}
      >
        <Descriptions
          size="small"
          column={1}
          bordered
          style={{ backgroundColor: '#fafafa' }}
        >
          {Object.entries(filteredDetails).map(([key, value]) => (
            <Descriptions.Item
              key={key}
              label={<span style={{ fontWeight: 'bold', color: '#262626' }}>{key}</span>}
            >
              <span style={{ color: '#262626' }}>
                {typeof value === 'object' ? (
                  <pre style={{
                    margin: 0,
                    padding: '8px',
                    backgroundColor: '#f5f5f5',
                    borderRadius: '4px',
                    fontSize: '12px',
                    color: '#262626'
                  }}>
                    {JSON.stringify(value, null, 2)}
                  </pre>
                ) : (
                  String(value)
                )}
              </span>
            </Descriptions.Item>
          ))}
        </Descriptions>
      </Modal>
    );
  };

  const columns = [
    {
      title: '时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (time) => dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
      sorter: true,
    },
    {
      title: '操作人',
      dataIndex: 'user_name',
      key: 'user_name',
      width: 120,
      render: (name, record) => (
        <div>
          <div>{name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.user_username}
          </div>
        </div>
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 150,
      render: (action) => getActionTag(action),
    },
    {
      title: '目标类型',
      dataIndex: 'target_type',
      key: 'target_type',
      width: 100,
      render: (type) => type ? <Tag color="default">{type}</Tag> : '-',
    },
    {
      title: '目标ID',
      dataIndex: 'target_id',
      key: 'target_id',
      width: 80,
      render: (id) => id || '-',
    },
    {
      title: '详情',
      dataIndex: 'details',
      key: 'details',
      width: 100,
      render: (details) => formatDetails(details),
    },
    {
      title: 'IP地址',
      dataIndex: 'ip_address',
      key: 'ip_address',
      width: 120,
      render: (ip) => ip || '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, record) => (
        <Popconfirm
          title="确定删除此操作日志吗？"
          description="删除后无法恢复"
          onConfirm={() => handleDeleteLog(record.id)}
          okText="确定"
          cancelText="取消"
        >
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
          >
            删除
          </Button>
        </Popconfirm>
      ),
    },
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={2}>
            <HistoryOutlined /> 操作日志
          </Title>
        </Col>
        <Col>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => fetchLogs()}
              loading={loading}
            >
              刷新
            </Button>
            <Popconfirm
              title="批量删除选中日志"
              description={`确定删除选中的 ${selectedRowKeys.length} 条日志吗？`}
              onConfirm={handleBatchDeleteSelected}
              disabled={selectedRowKeys.length === 0}
              okText="确定"
              cancelText="取消"
            >
              <Button
                danger
                icon={<DeleteOutlined />}
                disabled={selectedRowKeys.length === 0}
              >
                删除选中 ({selectedRowKeys.length})
              </Button>
            </Popconfirm>
            <Button
              icon={<ClearOutlined />}
              onClick={() => setBatchDeleteModalVisible(true)}
            >
              批量清理
            </Button>
            <Popconfirm
              title="清空所有操作日志"
              description="⚠️ 这将删除所有操作日志，此操作不可恢复！"
              onConfirm={handleClearAllLogs}
              okText="确定清空"
              cancelText="取消"
              icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
            >
              <Button danger type="primary">
                清空全部
              </Button>
            </Popconfirm>
          </Space>
        </Col>
      </Row>

      {/* 筛选条件 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Select
              placeholder="选择操作人"
              allowClear
              style={{ width: '100%' }}
              value={filters.user_id || undefined}
              onChange={(value) => handleFilterChange('user_id', value)}
            >
              {administrators.map(admin => (
                <Option key={admin.id} value={admin.id}>
                  {admin.name} ({admin.username})
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <Search
              placeholder="搜索操作类型"
              allowClear
              value={filters.action}
              onChange={(e) => handleFilterChange('action', e.target.value)}
              onSearch={(value) => handleFilterChange('action', value)}
            />
          </Col>
          <Col span={8}>
            <RangePicker
              style={{ width: '100%' }}
              value={filters.start_date && filters.end_date ? [
                dayjs(filters.start_date),
                dayjs(filters.end_date)
              ] : null}
              onChange={handleDateRangeChange}
              placeholder={['开始日期', '结束日期']}
            />
          </Col>
          <Col span={4}>
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={() => fetchLogs()}
              loading={loading}
              style={{ width: '100%' }}
            >
              搜索
            </Button>
          </Col>
        </Row>
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={logs}
          rowKey="id"
          loading={loading}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
            getCheckboxProps: (record) => ({
              name: record.id,
            }),
          }}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            pageSizeOptions: ['10', '20', '50', '100']
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 批量删除模态框 */}
      <Modal
        title="批量清理操作日志"
        open={batchDeleteModalVisible}
        onCancel={() => setBatchDeleteModalVisible(false)}
        footer={null}
        width={500}
      >
        <div style={{ padding: '20px 0' }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <div>
              <h4>删除指定日期之前的日志</h4>
              <DatePicker
                placeholder="选择日期"
                style={{ width: '100%', marginBottom: 10 }}
                onChange={(date) => {
                  if (date) {
                    Modal.confirm({
                      title: '确认删除',
                      content: `确定删除 ${date.format('YYYY-MM-DD')} 之前的所有操作日志吗？`,
                      onOk: () => handleDeleteBeforeDate(date.format('YYYY-MM-DD')),
                    });
                  }
                }}
              />
              <div style={{ color: '#666', fontSize: '12px' }}>
                将删除选择日期之前的所有操作日志
              </div>
            </div>
          </Space>
        </div>
      </Modal>

      {/* 详情查看模态框 */}
      {renderDetailModal()}
    </div>
  );
};

export default OperationLogs;
