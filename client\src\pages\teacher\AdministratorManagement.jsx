import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Space,
  message,
  Popconfirm,
  Tag,
  Row,
  Col,
  Typography,
  Checkbox
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  KeyOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { adminAPI } from '../../utils/api';

const { Title } = Typography;
const { Option } = Select;

const AdministratorManagement = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [administrators, setAdministrators] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [editingAdmin, setEditingAdmin] = useState(null);
  const [form] = Form.useForm();
  const [passwordForm] = Form.useForm();

  useEffect(() => {
    fetchAdministrators();
    fetchPermissions();
  }, []);

  const fetchAdministrators = async () => {
    try {
      setLoading(true);
      const response = await adminAPI.getAdministrators();
      setAdministrators(response.data);
    } catch (error) {
      console.error('获取管理员列表失败:', error);
      message.error('获取管理员列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchPermissions = async () => {
    try {
      const response = await adminAPI.getPermissions();
      setPermissions(response.data);
    } catch (error) {
      console.error('获取权限列表失败:', error);
      message.error('获取权限列表失败');
    }
  };

  const handleSubmit = async (values) => {
    try {
      setLoading(true);
      
      if (editingAdmin) {
        await adminAPI.updateAdministrator(editingAdmin.id, values);
        message.success('管理员信息更新成功');
      } else {
        await adminAPI.createAdministrator(values);
        message.success('管理员创建成功');
      }

      setModalVisible(false);
      form.resetFields();
      setEditingAdmin(null);
      fetchAdministrators();
    } catch (error) {
      console.error('操作失败:', error);
      message.error(error.response?.data?.message || '操作失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      await adminAPI.deleteAdministrator(id);
      message.success('管理员删除成功');
      fetchAdministrators();
    } catch (error) {
      console.error('删除失败:', error);
      message.error(error.response?.data?.message || '删除失败');
    }
  };

  const handleResetPassword = async (values) => {
    try {
      setLoading(true);
      await adminAPI.resetAdministratorPassword(editingAdmin.id, values);
      message.success('密码重置成功');
      setPasswordModalVisible(false);
      passwordForm.resetFields();
      setEditingAdmin(null);
    } catch (error) {
      console.error('重置密码失败:', error);
      message.error(error.response?.data?.message || '重置密码失败');
    } finally {
      setLoading(false);
    }
  };

  const getPermissionTags = (adminPermissions) => {
    return adminPermissions.map(permission => {
      const permissionInfo = permissions.find(p => p.value === permission);
      return (
        <Tag key={permission} color="blue" style={{ marginBottom: 4 }}>
          {permissionInfo?.name || permission}
        </Tag>
      );
    });
  };

  const columns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 120,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },
    {
      title: '类型',
      dataIndex: 'is_super_admin',
      key: 'is_super_admin',
      width: 100,
      render: (isSuperAdmin) => (
        <Tag color={isSuperAdmin ? 'red' : 'blue'}>
          {isSuperAdmin ? '超级管理员' : '普通管理员'}
        </Tag>
      ),
    },
    {
      title: '权限',
      dataIndex: 'permissions',
      key: 'permissions',
      width: 300,
      render: (permissions) => (
        <div style={{ maxHeight: 100, overflowY: 'auto' }}>
          {getPermissionTags(permissions)}
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (time) => time ? new Date(time).toLocaleString('zh-CN') : '-',
    },
    {
      title: '最后登录',
      dataIndex: 'last_login',
      key: 'last_login',
      width: 180,
      render: (time) => time ? new Date(time).toLocaleString('zh-CN') : '从未登录',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          {!record.is_super_admin && (
            <>
              <Button
                type="link"
                size="small"
                icon={<EditOutlined />}
                onClick={() => {
                  setEditingAdmin(record);
                  form.setFieldsValue({
                    name: record.name,
                    permissions: record.permissions
                  });
                  setModalVisible(true);
                }}
              >
                编辑
              </Button>
              <Button
                type="link"
                size="small"
                icon={<KeyOutlined />}
                onClick={() => {
                  setEditingAdmin(record);
                  setPasswordModalVisible(true);
                }}
              >
                重置密码
              </Button>
              <Popconfirm
                title="确定删除此管理员吗？"
                description="删除后无法恢复"
                onConfirm={() => handleDelete(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="link"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                >
                  删除
                </Button>
              </Popconfirm>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={2}>
            <UserOutlined /> 管理员管理
          </Title>
        </Col>
        <Col>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingAdmin(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              创建管理员
            </Button>
            <Button
              icon={<HistoryOutlined />}
              onClick={() => navigate('/teacher/operation-logs')}
            >
              操作日志
            </Button>
          </Space>
        </Col>
      </Row>

      <Card>
        <Table
          columns={columns}
          dataSource={administrators}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 创建/编辑管理员模态框 */}
      <Modal
        title={editingAdmin ? '编辑管理员' : '创建管理员'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
          setEditingAdmin(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          {!editingAdmin && (
            <>
              <Form.Item
                label="用户名"
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' }
                ]}
              >
                <Input placeholder="请输入用户名" />
              </Form.Item>

              <Form.Item
                label="密码"
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6个字符' }
                ]}
              >
                <Input.Password placeholder="请输入密码" />
              </Form.Item>
            </>
          )}

          <Form.Item
            label="姓名"
            name="name"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>

          <Form.Item
            label="权限"
            name="permissions"
            rules={[{ required: true, message: '请选择权限' }]}
          >
            <Checkbox.Group>
              <Row>
                {permissions.map(permission => (
                  <Col span={12} key={permission.value} style={{ marginBottom: 8 }}>
                    <Checkbox value={permission.value}>
                      {permission.name}
                    </Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button onClick={() => {
                setModalVisible(false);
                form.resetFields();
                setEditingAdmin(null);
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingAdmin ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 重置密码模态框 */}
      <Modal
        title="重置密码"
        open={passwordModalVisible}
        onCancel={() => {
          setPasswordModalVisible(false);
          passwordForm.resetFields();
          setEditingAdmin(null);
        }}
        footer={null}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handleResetPassword}
        >
          <Form.Item
            label="新密码"
            name="new_password"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password placeholder="请输入新密码" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button onClick={() => {
                setPasswordModalVisible(false);
                passwordForm.resetFields();
                setEditingAdmin(null);
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                重置密码
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdministratorManagement;
