import { createClient } from '@supabase/supabase-js';

// Supabase 配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.warn('⚠️ Supabase 配置缺失，使用内存数据存储');
}

// 创建 Supabase 客户端
const supabase = supabaseUrl && supabaseKey ? createClient(supabaseUrl, supabaseKey) : null;

// 数据库操作封装
class DatabaseService {
  constructor() {
    this.useSupabase = !!supabase;
    
    // 内存存储（当 Supabase 不可用时）
    this.memoryStore = {
      users: [],
      certificates: [],
      auditActivities: [],
      operationLogs: [],
      systemSettings: new Map()
    };
    
    if (!this.useSupabase) {
      this.initializeMemoryData();
    }
  }

  // 初始化内存数据
  initializeMemoryData() {
    // 创建默认管理员
    this.memoryStore.users.push({
      id: 1,
      username: 'admin',
      name: '系统管理员',
      password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
      role: 'teacher',
      is_super_admin: true,
      permissions: ['student_management', 'certificate_audit', 'grade_management', 'grade_export', 'activity_management', 'system_settings', 'admin_management', 'log_management'],
      created_at: new Date().toISOString()
    });

    // 默认系统设置
    this.memoryStore.systemSettings.set('certificate_submission_enabled', 'true');
    this.memoryStore.systemSettings.set('certificate_submission_deadline', null);
    this.memoryStore.systemSettings.set('certificate_submission_start', null);
    this.memoryStore.systemSettings.set('announcement', '');
  }

  // 用户相关操作
  async getUsers(filters = {}) {
    if (this.useSupabase) {
      let query = supabase.from('users').select('*');
      
      if (filters.role) {
        query = query.eq('role', filters.role);
      }
      if (filters.username) {
        query = query.eq('username', filters.username);
      }
      if (filters.student_id) {
        query = query.eq('student_id', filters.student_id);
      }
      
      const { data, error } = await query;
      if (error) throw error;
      return data;
    } else {
      let users = [...this.memoryStore.users];
      
      if (filters.role) {
        users = users.filter(u => u.role === filters.role);
      }
      if (filters.username) {
        users = users.filter(u => u.username === filters.username);
      }
      if (filters.student_id) {
        users = users.filter(u => u.student_id === filters.student_id);
      }
      
      return users;
    }
  }

  async getUserById(id) {
    if (this.useSupabase) {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', id)
        .single();
      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } else {
      return this.memoryStore.users.find(u => u.id === id);
    }
  }

  async createUser(userData) {
    if (this.useSupabase) {
      const { data, error } = await supabase
        .from('users')
        .insert([userData])
        .select()
        .single();
      if (error) throw error;
      return data;
    } else {
      const newUser = {
        id: Math.max(...this.memoryStore.users.map(u => u.id), 0) + 1,
        ...userData,
        created_at: new Date().toISOString()
      };
      this.memoryStore.users.push(newUser);
      return newUser;
    }
  }

  async updateUser(id, updates) {
    if (this.useSupabase) {
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      if (error) throw error;
      return data;
    } else {
      const userIndex = this.memoryStore.users.findIndex(u => u.id === id);
      if (userIndex === -1) throw new Error('用户不存在');
      
      this.memoryStore.users[userIndex] = {
        ...this.memoryStore.users[userIndex],
        ...updates,
        updated_at: new Date().toISOString()
      };
      return this.memoryStore.users[userIndex];
    }
  }

  async deleteUser(id) {
    if (this.useSupabase) {
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', id);
      if (error) throw error;
    } else {
      const userIndex = this.memoryStore.users.findIndex(u => u.id === id);
      if (userIndex === -1) throw new Error('用户不存在');
      this.memoryStore.users.splice(userIndex, 1);
    }
  }

  // 证书相关操作
  async getCertificates(filters = {}) {
    if (this.useSupabase) {
      let query = supabase.from('certificates').select('*');
      
      if (filters.user_id) {
        query = query.eq('user_id', filters.user_id);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.audit_activity_id) {
        query = query.eq('audit_activity_id', filters.audit_activity_id);
      }
      
      const { data, error } = await query.order('created_at', { ascending: false });
      if (error) throw error;
      return data;
    } else {
      let certificates = [...this.memoryStore.certificates];
      
      if (filters.user_id) {
        certificates = certificates.filter(c => c.user_id === filters.user_id);
      }
      if (filters.status) {
        certificates = certificates.filter(c => c.status === filters.status);
      }
      if (filters.audit_activity_id) {
        certificates = certificates.filter(c => c.audit_activity_id === filters.audit_activity_id);
      }
      
      return certificates.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    }
  }

  async createCertificate(certData) {
    if (this.useSupabase) {
      const { data, error } = await supabase
        .from('certificates')
        .insert([certData])
        .select()
        .single();
      if (error) throw error;
      return data;
    } else {
      const newCert = {
        id: Math.max(...this.memoryStore.certificates.map(c => c.id), 0) + 1,
        ...certData,
        created_at: new Date().toISOString()
      };
      this.memoryStore.certificates.push(newCert);
      return newCert;
    }
  }

  // 操作日志
  async createLog(logData) {
    if (this.useSupabase) {
      const { error } = await supabase
        .from('operation_logs')
        .insert([logData]);
      if (error) console.error('日志记录失败:', error);
    } else {
      const newLog = {
        id: Math.max(...this.memoryStore.operationLogs.map(l => l.id), 0) + 1,
        ...logData,
        timestamp: new Date().toISOString()
      };
      this.memoryStore.operationLogs.push(newLog);
      
      // 保持日志数量在合理范围内
      if (this.memoryStore.operationLogs.length > 10000) {
        this.memoryStore.operationLogs = this.memoryStore.operationLogs.slice(-5000);
      }
    }
  }
}

// 创建数据库服务实例
const db = new DatabaseService();

export {
  supabase,
  db
};
