import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Progress, Typography, Spin, Alert, Select, Space } from 'antd';
import { TrophyOutlined, FileTextOutlined, CheckCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { scoreAPI, certificateAPI, auditActivityAPI } from '../../utils/api';

const { Title, Text } = Typography;
const { Option } = Select;

const StudentHome = () => {
  const [loading, setLoading] = useState(true);
  const [scoreData, setScoreData] = useState(null);
  const [certificateStats, setCertificateStats] = useState({
    total: 0,
    approved: 0,
    pending: 0,
    rejected: 0
  });
  const [auditActivities, setAuditActivities] = useState([]);
  const [selectedActivity, setSelectedActivity] = useState(null);

  useEffect(() => {
    fetchAuditActivities();
    fetchData();
  }, []);

  useEffect(() => {
    if (selectedActivity !== null) {
      fetchData();
    }
  }, [selectedActivity]);

  const fetchAuditActivities = async () => {
    try {
      const response = await auditActivityAPI.getActivities();
      const activities = response.data?.activities || response.data || [];
      const activeActivities = activities.filter(activity =>
        activity.status === 'active' &&
        (!activity.end_date || new Date(activity.end_date + 'T23:59:59') >= new Date())
      );
      setAuditActivities(activeActivities);

      // 默认选择第一个审核活动
      if (activeActivities.length > 0 && selectedActivity === null) {
        setSelectedActivity(activeActivities[0].id);
      }
    } catch (error) {
      console.error('获取审核活动失败:', error);
    }
  };

  const fetchData = async () => {
    try {
      setLoading(true);

      // 获取成绩数据（如果有选择活动则基于活动，否则显示全部）
      const scoreParams = selectedActivity ? { audit_activity_id: selectedActivity } : {};
      const scoreResponse = await scoreAPI.getMyScore(scoreParams);
      setScoreData(scoreResponse.data?.score);

      // 获取证书统计（如果有选择活动则基于活动，否则显示全部）
      const certParams = {
        limit: 1000,
        ...(selectedActivity && { audit_activity_id: selectedActivity })
      };
      const certResponse = await certificateAPI.getMyCertificates(certParams);
      const certificates = certResponse.data?.certificates || [];

      const stats = {
        total: certificates.length,
        approved: certificates.filter(c => c.status === 'approved').length,
        pending: certificates.filter(c => c.status === 'pending').length,
        rejected: certificates.filter(c => c.status === 'rejected').length,
      };
      setCertificateStats(stats);

    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  const score = scoreData || {
    base_score: 25,
    professional_score: 0,
    sports_arts_score: 0,
    moral_score: 0,
    total_score: 25,
    final_score: 5
  };

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>首页概览</Title>

        {/* 活动选择器 */}
        <Space>
          <Text>审核活动：</Text>
          <Select
            style={{ width: 200 }}
            placeholder="选择审核活动"
            value={selectedActivity}
            onChange={setSelectedActivity}
            allowClear
          >
            {auditActivities.map(activity => (
              <Option key={activity.id} value={activity.id}>
                {activity.name}
              </Option>
            ))}
          </Select>
        </Space>
      </div>

      {/* 活动提示 */}
      {selectedActivity ? (
        <Alert
          message={`当前显示活动：${auditActivities.find(a => a.id === selectedActivity)?.name || '未知活动'}`}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      ) : (
        <Alert
          message="当前显示全部活动的汇总数据"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 成绩概览卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="最终成绩"
              value={score.final_score}
              precision={2}
              suffix="分"
              prefix={<TrophyOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总分"
              value={score.total_score}
              precision={2}
              suffix="分"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已提交证书"
              value={certificateStats.total}
              suffix="个"
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已通过审核"
              value={certificateStats.approved}
              suffix="个"
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 成绩详情 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="成绩详情" className="score-card">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12}>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>基础分值</Text>
                  <Progress
                    percent={(score.base_score / 25) * 100}
                    format={() => `${score.base_score}/25`}
                    strokeColor="#52c41a"
                  />
                </div>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>专业类活动分值</Text>
                  <Progress
                    percent={(score.professional_score / 30) * 100}
                    format={() => `${score.professional_score}/30`}
                    strokeColor="#1890ff"
                  />
                </div>
              </Col>
              <Col xs={24} sm={12}>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>体育美育综合活动分值</Text>
                  <Progress
                    percent={(score.sports_arts_score / 20) * 100}
                    format={() => `${score.sports_arts_score}/20`}
                    strokeColor="#722ed1"
                  />
                </div>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>文明品德综合活动分值</Text>
                  <Progress
                    percent={(score.moral_score / 25) * 100}
                    format={() => `${score.moral_score}/25`}
                    strokeColor="#fa8c16"
                  />
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
        
        <Col xs={24} lg={8}>
          <Card title="证书状态统计">
            <div style={{ marginBottom: 16 }}>
              <Row justify="space-between">
                <Text>待审核</Text>
                <Text strong style={{ color: '#faad14' }}>
                  <ClockCircleOutlined /> {certificateStats.pending}
                </Text>
              </Row>
            </div>
            <div style={{ marginBottom: 16 }}>
              <Row justify="space-between">
                <Text>已通过</Text>
                <Text strong style={{ color: '#52c41a' }}>
                  <CheckCircleOutlined /> {certificateStats.approved}
                </Text>
              </Row>
            </div>
            <div style={{ marginBottom: 16 }}>
              <Row justify="space-between">
                <Text>已拒绝</Text>
                <Text strong style={{ color: '#ff4d4f' }}>
                  {certificateStats.rejected}
                </Text>
              </Row>
            </div>
            
            {certificateStats.pending > 0 && (
              <Alert
                message="提醒"
                description={`您有 ${certificateStats.pending} 个证书正在审核中，请耐心等待。`}
                type="info"
                showIcon
                style={{ marginTop: 16 }}
              />
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default StudentHome;
