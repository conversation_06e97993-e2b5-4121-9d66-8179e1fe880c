import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import { Form, Input, Button, Card, Typography, message } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { authAPI } from './utils/api';
import StudentLayout from './pages/student/Layout';
import TeacherLayout from './pages/teacher/Layout';

const { Title } = Typography;

// 简化的登录组件
const Login = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();



  const onFinish = async (values) => {
    setLoading(true);


    try {

      const loginData = {
        student_id: values.username,
        password: values.password
      };


      const response = await authAPI.login(loginData);


      // 后端返回的是直接的数据，没有success字段
      if (response.token && response.user) {
        // 保存用户信息到localStorage，确保包含权限信息
        localStorage.setItem('token', response.token);

        const userInfo = {
          ...response.user,
          permissions: response.user.permissions || [],
          is_super_admin: response.user.is_super_admin || false
        };
        localStorage.setItem('user', JSON.stringify(userInfo));

        message.success(response.message || '登录成功！');


        // 根据用户角色跳转
        if (response.user.role === 'teacher') {

          navigate('/teacher/home');
        } else {

          navigate('/student/home');
        }
      } else {

        message.error(response.message || '登录失败');
      }
    } catch (error) {

      if (error.response && error.response.data && error.response.data.message) {
        message.error(error.response.data.message);
      } else {
        message.error('登录失败，请检查账号和密码');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }}>
      <Card style={{ width: 400, boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}>
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={2} style={{ color: '#1890ff', marginBottom: 8 }}>
            学生素质测评系统
          </Title>
          <p style={{ color: '#666', margin: 0 }}>请输入您的用户名和密码</p>
        </div>

        <Form
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          size="large"

        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: '请输入用户名或学号' }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名或学号"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{ width: '100%' }}

            >
              登录
            </Button>
          </Form.Item>
        </Form>


      </Card>
    </div>
  );
};


// 路由保护组件
const ProtectedRoute = ({ children, requireRole }) => {
  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  if (!token) {
    return <Navigate to="/login" replace />;
  }

  if (requireRole && user.role !== requireRole) {
    return <Navigate to="/login" replace />;
  }

  return children;
};

function App() {
  return (
    <Router
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true
      }}
    >
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/teacher/*" element={
          <ProtectedRoute requireRole="teacher">
            <TeacherLayout />
          </ProtectedRoute>
        } />
        <Route path="/student/*" element={
          <ProtectedRoute requireRole="student">
            <StudentLayout />
          </ProtectedRoute>
        } />
        <Route path="/" element={<Navigate to="/login" replace />} />
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    </Router>
  );
}

export default App;
