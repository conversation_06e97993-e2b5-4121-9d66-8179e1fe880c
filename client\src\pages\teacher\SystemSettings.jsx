import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Switch, 
  DatePicker, 
  Input, 
  Button, 
  message, 
  Space,
  Typography,
  Divider,
  Alert
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { settingsAPI } from '../../utils/api';
import dayjs from 'dayjs';

const { TextArea } = Input;
const { Title, Text } = Typography;

const SystemSettings = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState({});

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await settingsAPI.getSettings();
      const settingsData = response.data;
      
      setSettings(settingsData);
      form.setFieldsValue({
        certificate_submission_enabled: settingsData.certificate_submission_enabled,
        certificate_submission_start: settingsData.certificate_submission_start ? 
          dayjs(settingsData.certificate_submission_start) : null,
        certificate_submission_deadline: settingsData.certificate_submission_deadline ? 
          dayjs(settingsData.certificate_submission_deadline) : null,
        announcement: settingsData.announcement
      });
    } catch (error) {
      console.error('获取系统设置失败:', error);
      message.error('获取系统设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values) => {
    try {
      setLoading(true);
      
      const updateData = {
        certificate_submission_enabled: values.certificate_submission_enabled,
        certificate_submission_start: values.certificate_submission_start ? 
          values.certificate_submission_start.format('YYYY-MM-DD HH:mm:ss') : null,
        certificate_submission_deadline: values.certificate_submission_deadline ? 
          values.certificate_submission_deadline.format('YYYY-MM-DD HH:mm:ss') : null,
        announcement: values.announcement
      };
      
      await settingsAPI.updateSettings(updateData);
      message.success('系统设置保存成功');
      fetchSettings();
    } catch (error) {
      console.error('保存系统设置失败:', error);
      message.error('保存系统设置失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card 
        title={
          <Space>
            <SettingOutlined />
            <span>系统设置</span>
          </Space>
        }
        extra={
          <Button 
            icon={<ReloadOutlined />} 
            onClick={fetchSettings}
            loading={loading}
          >
            刷新
          </Button>
        }
      >
        <Alert
          message="系统设置说明"
          description="在这里可以配置证书提交的时间限制、系统公告等设置。修改后将立即生效。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={{
            certificate_submission_enabled: true,
            announcement: '欢迎使用学生素质测评系统！'
          }}
        >
          <Title level={4}>证书提交设置</Title>
          
          <Form.Item
            label="启用证书提交功能"
            name="certificate_submission_enabled"
            valuePropName="checked"
            extra="关闭后学生将无法提交新的证书"
          >
            <Switch 
              checkedChildren="开启" 
              unCheckedChildren="关闭"
            />
          </Form.Item>

          <Form.Item
            label="证书提交开始时间"
            name="certificate_submission_start"
            extra="设置后学生只能在指定时间后提交证书，留空表示无限制"
          >
            <DatePicker
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="选择开始时间"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            label="证书提交截止时间"
            name="certificate_submission_deadline"
            extra="设置后学生只能在截止时间前提交证书，留空表示无限制"
          >
            <DatePicker
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="选择截止时间"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Divider />

          <Title level={4}>系统公告</Title>
          
          <Form.Item
            label="系统公告内容"
            name="announcement"
            extra="将在学生登录后显示的公告信息"
          >
            <TextArea
              rows={4}
              placeholder="请输入系统公告内容..."
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Divider />

          <Form.Item style={{ marginBottom: 0 }}>
            <Space>
              <Button 
                type="primary" 
                htmlType="submit" 
                icon={<SaveOutlined />}
                loading={loading}
              >
                保存设置
              </Button>
              <Button onClick={() => form.resetFields()}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>

        <Divider />

        <div style={{ marginTop: 24 }}>
          <Title level={4}>当前设置状态</Title>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text>
              <strong>证书提交功能：</strong>
              {settings.certificate_submission_enabled ? 
                <Text type="success">已开启</Text> : 
                <Text type="danger">已关闭</Text>
              }
            </Text>
            <Text>
              <strong>提交开始时间：</strong>
              {settings.certificate_submission_start ? 
                settings.certificate_submission_start : 
                <Text type="secondary">无限制</Text>
              }
            </Text>
            <Text>
              <strong>提交截止时间：</strong>
              {settings.certificate_submission_deadline ? 
                settings.certificate_submission_deadline : 
                <Text type="secondary">无限制</Text>
              }
            </Text>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default SystemSettings;
