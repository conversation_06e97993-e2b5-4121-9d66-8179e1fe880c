import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  message, 
  Space,
  Typography,
  Row,
  Col,
  Popconfirm,
  Upload,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  TeamOutlined,
  UploadOutlined,
  DownloadOutlined,
  ReloadOutlined,
  SearchOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { userAPI, excelAPI } from '../../utils/api';

const { Title, Text } = Typography;
const { Option } = Select;
const { Search } = Input;

const StudentManagement = () => {
  const [loading, setLoading] = useState(false);
  const [students, setStudents] = useState([]);
  const [classes, setClasses] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingStudent, setEditingStudent] = useState(null);
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [statistics, setStatistics] = useState({
    total: 0,
    withCertificates: 0,
    withScores: 0
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  useEffect(() => {
    fetchStudents();
    fetchClasses();
  }, []);

  const fetchStudents = async (params = {}) => {
    try {
      setLoading(true);
      const response = await userAPI.getStudents({
        page: pagination.current,
        limit: pagination.pageSize,
        search: searchText,
        class_name: selectedClass,
        ...params
      });

      setStudents(response.data.students || []);
      setPagination(prev => ({
        ...prev,
        total: response.data.total || 0
      }));

      // 更新统计数据
      setStatistics({
        total: response.data.total || 0,
        withCertificates: response.data.statistics?.withCertificates || 0,
        withScores: response.data.statistics?.withScores || 0
      });
    } catch (error) {
      console.error('获取学生列表失败:', error);
      message.error('获取学生列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchClasses = async () => {
    try {
      const response = await userAPI.getClasses();
      setClasses(response.data.classes || []);
    } catch (error) {
      console.error('获取班级列表失败:', error);
    }
  };

  const handleSubmit = async (values) => {
    try {
      if (editingStudent) {
        await userAPI.updateStudent(editingStudent.id, values);
        message.success('学生信息更新成功');
      } else {
        await userAPI.createStudent(values);
        message.success('学生添加成功');
      }

      setModalVisible(false);
      form.resetFields();
      setEditingStudent(null);
      fetchStudents();
    } catch (error) {
      console.error('操作失败:', error);
      message.error('操作失败: ' + (error.response?.data?.message || error.message));
    }
  };

  const handleEdit = (record) => {
    setEditingStudent(record);
    form.setFieldsValue({
      student_id: record.student_id,
      name: record.name,
      class_name: record.class_name
    });
    setModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await userAPI.deleteStudent(id);
      message.success('删除成功');
      fetchStudents();
      setSelectedRowKeys([]);
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的学生');
      return;
    }

    try {
      const response = await userAPI.batchDeleteStudents({ student_ids: selectedRowKeys });
      message.success(response.data.message || `成功删除 ${selectedRowKeys.length} 个学生`);
      fetchStudents();
      setSelectedRowKeys([]);
    } catch (error) {
      console.error('批量删除失败:', error);
      message.error('批量删除失败');
    }
  };

  const handleDeleteAll = async () => {
    try {
      if (statistics.total === 0) {
        message.warning('没有学生可以删除');
        return;
      }

      const response = await userAPI.clearAllStudents();
      message.success(response.data.message || `成功删除所有学生`);
      fetchStudents();
      setSelectedRowKeys([]);
    } catch (error) {
      console.error('删除所有学生失败:', error);
      message.error('删除所有学生失败');
    }
  };

  const handleResetPassword = async (id) => {
    try {
      await userAPI.resetStudentPassword(id);
      message.success('密码重置成功，新密码为学号');
    } catch (error) {
      console.error('密码重置失败:', error);
      message.error('密码重置失败');
    }
  };

  const handleExcelImport = async (file) => {
    try {
      setLoading(true);
      const response = await excelAPI.importStudents(file);
      message.success(`导入成功：${response.data.success_count} 个学生`);
      if (response.data.error_count > 0) {
        message.warning(`${response.data.error_count} 个学生导入失败`);
      }
      fetchStudents();
    } catch (error) {
      console.error('导入失败:', error);
      message.error('导入失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      const response = await excelAPI.downloadStudentTemplate();
      const blob = new Blob([response], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = '学生信息导入模板.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('下载模板失败:', error);
      message.error('下载模板失败');
    }
  };

  const handleSearch = (value) => {
    setSearchText(value);
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchStudents({ search: value, page: 1 });
  };

  const handleClassFilter = (value) => {
    setSelectedClass(value);
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchStudents({ class_name: value, page: 1 });
  };

  const handleTableChange = (paginationInfo) => {
    setPagination(paginationInfo);
    fetchStudents({
      page: paginationInfo.current,
      limit: paginationInfo.pageSize
    });
  };

  const columns = [
    {
      title: '学号',
      dataIndex: 'student_id',
      key: 'student_id',
      width: 130,
      fixed: 'left',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 100,
      fixed: 'left',
    },
    {
      title: '班级',
      dataIndex: 'class_name',
      key: 'class_name',
      width: 150,
      ellipsis: true,
    },
    {
      title: '证书数量',
      key: 'certificate_count',
      width: 100,
      align: 'center',
      render: (_, record) => (
        <Text>{record.certificate_count || 0}</Text>
      ),
    },
    {
      title: '最终成绩',
      key: 'final_score',
      width: 100,
      align: 'center',
      render: (_, record) => (
        <Text strong style={{ color: '#1890ff' }}>
          {record.final_score ? `${record.final_score.toFixed(2)}分` : '-'}
        </Text>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (time) => {
        if (!time) return '-';
        try {
          return new Date(time).toLocaleDateString('zh-CN');
        } catch (error) {
          return '-';
        }
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => handleResetPassword(record.id)}
          >
            重置密码
          </Button>
          <Popconfirm
            title="确定要删除这个学生吗？"
            description="删除后将无法恢复，相关证书也会被删除。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const uploadProps = {
    beforeUpload: (file) => {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel';
      if (!isExcel) {
        message.error('只能上传Excel文件！');
        return false;
      }
      handleExcelImport(file);
      return false;
    },
    showUploadList: false,
  };

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={2}>
            <TeamOutlined /> 学生管理
          </Title>
        </Col>
        <Col>
          <Space wrap>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleDownloadTemplate}
            >
              下载模板
            </Button>
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />}>
                批量导入
              </Button>
            </Upload>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingStudent(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              添加学生
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 危险操作区域 */}
      <Card size="small" style={{ marginBottom: 16, borderColor: '#ff4d4f' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Text type="warning">
              <ExclamationCircleOutlined /> 危险操作区域
            </Text>
          </Col>
          <Col>
            <Popconfirm
              title="确定要删除所有学生吗？"
              description={`将删除所有 ${statistics.total} 个学生及其相关证书，此操作不可恢复！`}
              onConfirm={handleDeleteAll}
              okText="确定删除"
              cancelText="取消"
              okButtonProps={{ danger: true }}
            >
              <Button
                danger
                size="small"
                icon={<DeleteOutlined />}
                disabled={statistics.total === 0}
              >
                删除所有学生 ({statistics.total})
              </Button>
            </Popconfirm>
          </Col>
        </Row>
      </Card>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索学号、姓名"
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Select
              placeholder="选择班级"
              allowClear
              style={{ width: '100%' }}
              onChange={handleClassFilter}
              value={selectedClass || undefined}
            >
              {classes.map(cls => (
                <Option key={cls.class_name} value={cls.class_name}>
                  {cls.class_name} ({cls.student_count}人)
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Button 
              icon={<ReloadOutlined />}
              onClick={() => {
                setSearchText('');
                setSelectedClass('');
                fetchStudents({ search: '', class_name: '', page: 1 });
              }}
            >
              重置
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                {statistics.total}
              </div>
              <div style={{ color: '#666' }}>学生总数</div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                {statistics.withCertificates}
              </div>
              <div style={{ color: '#666' }}>已提交证书</div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#faad14' }}>
                {statistics.withScores}
              </div>
              <div style={{ color: '#666' }}>有成绩学生</div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#f5222d' }}>
                {selectedRowKeys.length}
              </div>
              <div style={{ color: '#666' }}>已选择学生</div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 学生列表 */}
      <Card title={
        <Space>
          <TeamOutlined />
          <span>学生列表</span>
          <Text type="secondary">({statistics.total} 人)</Text>
        </Space>
      }>
        {/* 批量操作工具栏 */}
        {selectedRowKeys.length > 0 && (
          <div style={{
            marginBottom: 16,
            padding: 12,
            backgroundColor: '#e6f7ff',
            borderRadius: 6,
            border: '1px solid #91d5ff'
          }}>
            <Row justify="space-between" align="middle">
              <Col>
                <Text strong>
                  已选择 <Text type="primary">{selectedRowKeys.length}</Text> 个学生
                </Text>
              </Col>
              <Col>
                <Space>
                  <Button
                    size="small"
                    onClick={() => {
                      const allStudentIds = students.map(student => student.id);
                      setSelectedRowKeys(allStudentIds);
                    }}
                  >
                    全选所有 ({statistics.total})
                  </Button>
                  <Button
                    size="small"
                    onClick={() => setSelectedRowKeys([])}
                  >
                    取消选择
                  </Button>
                  <Popconfirm
                    title="确定要批量删除选中的学生吗？"
                    description="删除后将无法恢复，相关证书也会被删除。"
                    onConfirm={handleBatchDelete}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      type="primary"
                      danger
                      size="small"
                      icon={<DeleteOutlined />}
                    >
                      批量删除
                    </Button>
                  </Popconfirm>
                </Space>
              </Col>
            </Row>
          </div>
        )}

        <Table
          columns={columns}
          dataSource={students}
          rowKey="id"
          loading={loading}
          size="middle"
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
            onSelectAll: (selected, selectedRows, changeRows) => {
              if (selected) {
                // 全选所有学生（包括其他页面的）
                const allStudentIds = students.map(student => student.id);
                setSelectedRowKeys(allStudentIds);
              } else {
                // 取消全选
                setSelectedRowKeys([]);
              }
            },
            getCheckboxProps: (record) => ({
              name: record.name,
            }),
          }}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            pageSizeOptions: ['10', '20', '50', '100'],
          }}
          onChange={handleTableChange}
          scroll={{ x: 1000, y: 600 }}
          bordered
        />
      </Card>

      {/* 添加/编辑学生模态框 */}
      <Modal
        title={editingStudent ? '编辑学生' : '添加学生'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
          setEditingStudent(null);
        }}
        footer={null}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="student_id"
            label="学号"
            rules={[
              { required: true, message: '请输入学号' },
              { pattern: /^\d+$/, message: '学号只能包含数字' }
            ]}
          >
            <Input placeholder="请输入学号" disabled={!!editingStudent} />
          </Form.Item>

          <Form.Item
            name="name"
            label="姓名"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>

          <Form.Item
            name="class_name"
            label="班级"
            rules={[{ required: true, message: '请输入班级' }]}
          >
            <Input placeholder="请输入班级名称" />
          </Form.Item>

          <Divider />

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingStudent ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default StudentManagement;
