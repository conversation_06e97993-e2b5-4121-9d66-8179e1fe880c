import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  AutoComplete,
  Upload,
  message,
  Tag,
  Space,
  Typography,
  Row,
  Col,
  Divider,
  Alert
} from 'antd';
import {
  PlusOutlined,
  UploadOutlined,
  EditOutlined,
  DeleteOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { certificateAPI, categoryAPI, settingsAPI, auditActivityAPI } from '../../utils/api';

const { Title, Text } = Typography;
const { Option } = Select;

const CertificateManagement = () => {
  const [loading, setLoading] = useState(false);
  const [certificates, setCertificates] = useState([]);
  const [categories, setCategories] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [batchModalVisible, setBatchModalVisible] = useState(false);
  const [editingCertificate, setEditingCertificate] = useState(null);
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  const [batchCertificates, setBatchCertificates] = useState([{
    key: 1,
    category_id: null,
    certificate_name: '',
    level: '',
    award_date: '',
    team_size: 1,
    role: 'individual',
    files: []
  }]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [systemSettings, setSystemSettings] = useState({});
  const [auditActivities, setAuditActivities] = useState([]);
  const [selectedActivity, setSelectedActivity] = useState(null);

  useEffect(() => {
    fetchCertificates();
    fetchCategories();
    fetchSystemSettings();
    fetchAuditActivities();
  }, []);



  const fetchCertificates = async () => {
    try {
      setLoading(true);
      const response = await certificateAPI.getMyCertificates();
      const certificateList = response.data.certificates || [];
      setCertificates(certificateList);
    } catch (error) {
      console.error('获取证书列表失败:', error);
      message.error('获取证书列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取当前审核活动的证书
  const getCurrentActivityCertificates = () => {
    if (!selectedActivity) {
      return [];
    }

    const filtered = certificates.filter(cert => {
      // 尝试两种比较方式：严格相等和类型转换后相等
      const match1 = cert.audit_activity_id === selectedActivity;
      const match2 = cert.audit_activity_id == selectedActivity;
      const match3 = parseInt(cert.audit_activity_id) === parseInt(selectedActivity);

      return match1 || match2 || match3;
    });

    return filtered;
  };

  const fetchCategories = async () => {
    try {
      const response = await categoryAPI.getCategories();
      const categoriesData = response.data.flat_categories || [];
      setCategories(categoriesData);
    } catch (error) {
      console.error('获取类别列表失败:', error);
    }
  };

  const fetchSystemSettings = async () => {
    try {
      const response = await settingsAPI.getSettings();
      setSystemSettings(response.data || {});
    } catch (error) {
      console.error('获取系统设置失败:', error);
    }
  };

  const fetchAuditActivities = async () => {
    try {
      const response = await auditActivityAPI.getActivities();

      // 兼容不同的数据结构
      const activities = response.data?.activities || response.data || [];

      const activeActivities = activities.filter(activity => {
        const isActive = activity.status === 'active';
        const isNotExpired = !activity.end_date || new Date(activity.end_date + 'T23:59:59') >= new Date();
        return isActive && isNotExpired;
      });

      setAuditActivities(activeActivities);

      // 默认选择第一个审核活动
      if (activeActivities.length > 0 && selectedActivity === null) {
        setSelectedActivity(activeActivities[0].id);
      }
    } catch (error) {
      console.error('获取审核活动失败:', error);
    }
  };

  const handleSubmit = async (values) => {
    try {
      // 强制检查是否选择了审核活动
      if (!selectedActivity) {
        message.error('请先选择要参与的审核活动');
        return;
      }

      // 检查提交数量限制（基于选定的审核活动）
      const category = getCurrentCategory(values.category_id);
      if (category) {
        const limit = getCategorySubmissionLimit(category.category_name);
        if (limit) {
          const alreadySubmittedCount = getSubmittedCertificateCount(category.category_name, selectedActivity);
          if (alreadySubmittedCount >= limit) {
            message.error(`在当前审核活动中，${category.category_name}类别最多只能提交${limit}个证书，您已提交${alreadySubmittedCount}个，无法继续提交`);
            return;
          }
        }
      }

      const formData = new FormData();
      formData.append('category_id', values.category_id);
      formData.append('audit_activity_id', selectedActivity); // 添加审核活动ID

      // 只有需要证书名称的类别才添加
      if (values.certificate_name) {
        formData.append('certificate_name', values.certificate_name);
      }

      formData.append('level', values.level);

      // 添加其他字段
      if (values.award_date) {
        formData.append('award_date', values.award_date);
      }
      if (values.team_size) {
        formData.append('team_size', values.team_size);
      }
      if (values.role) {
        formData.append('role', values.role);
      }

      if (fileList.length > 0) {
        const file = fileList[0].originFileObj || fileList[0];

        if (file && file instanceof File) {
          formData.append('file', file);
        }
      }

      if (editingCertificate) {
        await certificateAPI.updateCertificate(editingCertificate.id, formData);
        message.success('证书更新成功');
      } else {
        await certificateAPI.submitCertificate(formData);
        message.success('证书提交成功，等待审核');
      }

      setModalVisible(false);
      form.resetFields();
      setFileList([]);
      setEditingCertificate(null);
      setSelectedCategory(null);
      fetchCertificates();
    } catch (error) {
      console.error('提交失败:', error);
      const errorMessage = error.response?.data?.message || error.message || '提交失败，请重试';
      message.error(errorMessage);
    }
  };

  const handleEdit = (record) => {
    setEditingCertificate(record);
    form.setFieldsValue({
      category_id: record.category_id,
      certificate_name: record.certificate_name,
      level: record.level
    });
    setModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await certificateAPI.deleteCertificate(id);
      message.success('删除成功');
      fetchCertificates();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 批量提交证书
  const handleBatchSubmit = async (values) => {
    try {
      setLoading(true);

      // 强制检查是否选择了审核活动
      if (!selectedActivity) {
        message.error('请先选择要参与的审核活动');
        setLoading(false);
        return;
      }

      // 验证批量证书数据
      const errors = [];
      const validCertificates = [];
      const categoryCount = {}; // 统计每个类别的提交数量

      batchCertificates.forEach((cert, index) => {
        const category = getCurrentCategory(cert.category_id);

        if (!cert.category_id) {
          errors.push(`第${index + 1}个证书：请选择证书类别`);
          return;
        }

        if (!category) {
          errors.push(`第${index + 1}个证书：证书类别不存在`);
          return;
        }

        if ((needsCertificateName(category.category_name) || category.use_predefined_names) && !cert.certificate_name) {
          const fieldName = category.use_predefined_names ? '竞赛名称' :
                           category.category_name === '科学研究' ? '论文/专利名称' : '证书名称';
          errors.push(`第${index + 1}个证书：请${category.use_predefined_names ? '选择' : '填写'}${fieldName}`);
          return;
        }

        if (!cert.level) {
          errors.push(`第${index + 1}个证书：请选择获奖等级`);
          return;
        }

        if (!cert.files || cert.files.length === 0) {
          errors.push(`第${index + 1}个证书：请上传证书文件`);
          return;
        }

        // 统计类别数量
        const categoryName = category.category_name;
        categoryCount[categoryName] = (categoryCount[categoryName] || 0) + 1;

        validCertificates.push(cert);
      });

      // 检查类别数量限制（基于选定的审核活动，包括已提交的证书）
      Object.keys(categoryCount).forEach(categoryName => {
        const limit = getCategorySubmissionLimit(categoryName);
        const currentSubmissionCount = categoryCount[categoryName];
        const alreadySubmittedCount = getSubmittedCertificateCount(categoryName, selectedActivity);
        const totalCount = currentSubmissionCount + alreadySubmittedCount;

        if (limit && totalCount > limit) {
          errors.push(`在当前审核活动中，${categoryName}类别最多只能提交${limit}个证书，您已提交${alreadySubmittedCount}个，本次提交${currentSubmissionCount}个，总计${totalCount}个，超出限制`);
        }
      });

      if (errors.length > 0) {
        errors.forEach(error => message.error(error));
        return;
      }

      if (validCertificates.length === 0) {
        message.error('请至少填写一个完整的证书信息');
        return;
      }

      // 使用批量提交API
      let successCount = 0;
      let errorCount = 0;
      const submitErrors = [];

      try {
        const formData = new FormData();

        // 添加证书数据
        formData.append('certificates', JSON.stringify(validCertificates.map(cert => ({
          category_id: cert.category_id,
          certificate_name: cert.certificate_name || '',
          level: cert.level,
          award_date: cert.award_date || '',
          team_size: cert.team_size || 1,
          role: cert.role || 'individual',
          audit_activity_id: selectedActivity
        }))));

        // 添加所有文件
        validCertificates.forEach((cert) => {
          cert.files.forEach((file) => {
            const fileObj = file.originFileObj || file;
            if (fileObj && fileObj instanceof File) {
              formData.append('files', fileObj);
            }
          });
        });



        const response = await certificateAPI.batchSubmitCertificates(formData);

        if (response.data.success_count > 0) {
          message.success(`成功提交 ${response.data.success_count} 个证书${response.data.error_count > 0 ? `，${response.data.error_count} 个失败` : ''}`);
          successCount = response.data.success_count;
        }

        if (response.data.errors && response.data.errors.length > 0) {
          response.data.errors.forEach(error => {
            message.warning(error);
          });
          errorCount = response.data.error_count || 0;
        }

      } catch (error) {
        console.error('批量提交失败:', error);
        message.error(error.response?.data?.message || '批量提交失败');
        errorCount = validCertificates.length;
      }

      // 结果已在上面处理

      setBatchModalVisible(false);
      setFileList([]);
      setBatchCertificates([{
        key: 1,
        category_id: null,
        certificate_name: '',
        level: '',
        award_date: '',
        team_size: 1,
        role: 'individual',
        files: []
      }]);
      fetchCertificates();
    } catch (error) {
      console.error('批量提交失败:', error);
      message.error(error.response?.data?.message || '批量提交失败');
    } finally {
      setLoading(false);
    }
  };

  // 添加批量证书项
  const addBatchCertificate = () => {
    const newKey = Math.max(...batchCertificates.map(c => c.key)) + 1;
    setBatchCertificates([...batchCertificates, {
      key: newKey,
      category_id: null,
      certificate_name: '',
      level: '',
      award_date: '',
      team_size: 1,
      role: 'individual',
      files: []
    }]);
  };

  // 删除批量证书项
  const removeBatchCertificate = (key) => {
    if (batchCertificates.length > 1) {
      setBatchCertificates(batchCertificates.filter(c => c.key !== key));
    }
  };

  // 更新批量证书项
  const updateBatchCertificate = (key, field, value) => {
    setBatchCertificates(prevCertificates => {
      const newBatchCertificates = prevCertificates.map(cert =>
        cert.key === key ? { ...cert, [field]: value } : cert
      );
      return newBatchCertificates;
    });
  };

  // 更新批量证书文件
  const updateBatchCertificateFiles = (key, files) => {
    setBatchCertificates(batchCertificates.map(cert =>
      cert.key === key ? { ...cert, files: files } : cert
    ));
  };

  // 判断是否需要显示团队相关字段
  const needsTeamFields = (categoryName) => {
    return ['实践技能类竞赛', '体育竞赛', '文艺活动'].includes(categoryName);
  };

  // 判断是否需要显示角色字段
  const needsRoleField = (categoryName) => {
    return categoryName === '学生创新型项目';
  };

  // 判断是否需要证书名称字段
  const needsCertificateName = (categoryName) => {
    // 学生干部、新闻采编写不需要证书名称
    return !['学生干部', '新闻采编写'].includes(categoryName);
  };

  // 获取类别的提交数量限制
  const getCategorySubmissionLimit = (categoryName) => {
    const limits = {
      '体育竞赛': 2,
      '文艺活动': 2,
      '科学研究': 2, // 学术论文
      '学生干部': 1,
      // 其他类别无明确数量限制
    };
    return limits[categoryName] || null; // null表示无限制
  };

  // 获取已提交的证书数量（按类别和审核活动统计）
  const getSubmittedCertificateCount = (categoryName, activityId = null) => {
    return certificates.filter(cert => {
      const matchCategory = cert.category_name === categoryName;
      const matchActivity = activityId ? cert.audit_activity_id === activityId : true;
      const notRejected = cert.status !== 'rejected'; // 排除被拒绝的证书
      return matchCategory && matchActivity && notRejected;
    }).length;
  };

  // 获取获奖等级选项
  const getLevelOptions = (categoryName) => {
    if (categoryName === '专业认证') {
      return [
        { value: 'professional_high', label: '高级认证' },
        { value: 'professional_mid', label: '中级认证' },
        { value: 'professional_low', label: '初级认证' }
      ];
    } else if (categoryName === '学生干部') {
      return [
        { value: 'school_president', label: '校级以上学生组织主席团成员' },
        { value: 'college_president', label: '院学生会主席团成员/青协主席团成员' },
        { value: 'grade_leader', label: '年级长（团支书）' },
        { value: 'class_leader', label: '班长（团支书）' },
        { value: 'department_head', label: '年级委员/班委成员' },
        { value: 'society_president', label: '各社团主席团成员' },
        { value: 'student_union_minister', label: '校院学生会部长' },
        { value: 'society_minister', label: '各社团部长' },
        { value: 'funding_captain', label: '院资助站队长（副队长）' },
        { value: 'dormitory_head', label: '寝室长' },
        { value: 'teaching_liaison', label: '教学联络员' }
      ];
    } else if (categoryName === '体育竞赛') {
      return [
        { value: 'national_1', label: '国家级第一名' },
        { value: 'national_2', label: '国家级第二名' },
        { value: 'national_3', label: '国家级第三名' },
        { value: 'national_other', label: '国家级其他名次（4-8名）' },
        { value: 'provincial_1', label: '省级第一名' },
        { value: 'provincial_2', label: '省级第二名' },
        { value: 'provincial_3', label: '省级第三名' },
        { value: 'provincial_other', label: '省级其他名次（4-8名）' },
        { value: 'school_1', label: '校级第一名' },
        { value: 'school_2', label: '校级第二名' },
        { value: 'school_3', label: '校级第三名' },
        { value: 'school_other', label: '校级其他名次（4-8名）' },
        { value: 'college_1', label: '院级第一名' },
        { value: 'college_2', label: '院级第二名' },
        { value: 'college_3', label: '院级第三名' },
        { value: 'college_other', label: '院级其他名次（不加分）' }
      ];
    } else if (categoryName === '英语六级') {
      return [
        { value: 'pass', label: '通过' }
      ];
    } else if (categoryName === '科学研究') {
      return [
        { value: 'paper_core', label: '核心期刊论文' },
        { value: 'patent_inventor', label: '发明专利发明人' },
        { value: 'patent_participant', label: '发明专利参与者' }
      ];
    } else if (categoryName === '社会实践志愿服务') {
      return [
        { value: 'national_individual', label: '国家级先进个人/优秀志愿者' },
        { value: 'provincial_individual', label: '省级先进个人/优秀志愿者' },
        { value: 'school_individual', label: '校级先进个人/优秀志愿者' },
        { value: 'college_individual', label: '院级先进个人/优秀志愿者' },
        { value: 'national_key_team', label: '国家级重点团队成员' },
        { value: 'provincial_key_team', label: '省级重点团队成员' },
        { value: 'school_key_team', label: '校级重点团队成员' },
        { value: 'college_key_team', label: '院级重点团队成员' },
        { value: 'national_normal_team', label: '国家级非重点团队成员' },
        { value: 'provincial_normal_team', label: '省级非重点团队成员' },
        { value: 'school_normal_team', label: '校级非重点团队成员' },
        { value: 'college_normal_team', label: '院级非重点团队成员' }
      ];
    } else if (categoryName === '新闻采编写') {
      return [
        { value: 'news_writing', label: '新闻采编写' }
      ];
    } else {
      // 通用等级选项
      return [
        { value: 'national_1', label: '国家级一等奖' },
        { value: 'national_2', label: '国家级二等奖' },
        { value: 'national_3', label: '国家级三等奖' },
        { value: 'provincial_1', label: '省级一等奖' },
        { value: 'provincial_2', label: '省级二等奖' },
        { value: 'provincial_3', label: '省级三等奖' },
        { value: 'school_1', label: '校级一等奖' },
        { value: 'school_2', label: '校级二等奖' },
        { value: 'school_3', label: '校级三等奖' },
        { value: 'college_1', label: '院级一等奖' },
        { value: 'college_2', label: '院级二等奖' },
        { value: 'college_3', label: '院级三等奖' }
      ];
    }
  };

  // 获取当前选中的类别信息
  const getCurrentCategory = (categoryId) => {
    return categories.find(cat => cat.id === categoryId);
  };

  // 检查是否可以提交证书
  const canSubmitCertificate = () => {
    const now = new Date();

    // 检查是否选择了审核活动
    if (!selectedActivity) {
      return { canSubmit: false, reason: '请先选择审核活动' };
    }

    // 检查审核活动是否有效
    const currentActivity = auditActivities.find(a => a.id === selectedActivity);
    if (!currentActivity) {
      return { canSubmit: false, reason: '选择的审核活动无效' };
    }

    // 检查审核活动时间
    if (currentActivity.end_date && new Date(currentActivity.end_date + 'T23:59:59') < now) {
      return { canSubmit: false, reason: `审核活动"${currentActivity.name}"已结束` };
    }

    // 检查功能是否开启
    if (!systemSettings.certificate_submission_enabled) {
      return { canSubmit: false, reason: '证书提交功能已关闭' };
    }

    // 检查开始时间
    if (systemSettings.certificate_submission_start) {
      const startTime = new Date(systemSettings.certificate_submission_start);
      if (now < startTime) {
        return {
          canSubmit: false,
          reason: `证书提交将于 ${startTime.toLocaleString()} 开始`
        };
      }
    }

    // 检查截止时间
    if (systemSettings.certificate_submission_deadline) {
      const deadline = new Date(systemSettings.certificate_submission_deadline);
      if (now > deadline) {
        return {
          canSubmit: false,
          reason: `证书提交已于 ${deadline.toLocaleString()} 截止`
        };
      }
    }

    return { canSubmit: true, reason: '' };
  };

  // 检查是否还有类别可以提交（基于当前选定的审核活动）
  const hasAvailableCategories = () => {
    if (!selectedActivity) {
      return false; // 没有选择审核活动时不能提交
    }

    const limitedCategories = ['学生干部', '体育竞赛', '文艺活动', '科学研究'];

    // 检查是否还有无限制的类别可以提交
    const unlimitedCategories = categories.filter(cat =>
      !limitedCategories.includes(cat.category_name)
    );

    if (unlimitedCategories.length > 0) {
      return true; // 有无限制的类别可以提交
    }

    // 检查有限制的类别是否还有剩余名额（基于当前审核活动）
    return limitedCategories.some(categoryName => {
      const limit = getCategorySubmissionLimit(categoryName);
      const submitted = getSubmittedCertificateCount(categoryName, selectedActivity);
      return limit && submitted < limit;
    });
  };

  const getStatusTag = (status) => {
    const statusMap = {
      pending: { color: 'orange', text: '待审核' },
      approved: { color: 'green', text: '已通过' },
      rejected: { color: 'red', text: '已拒绝' }
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getLevelText = (level) => {
    const levelMap = {
      // 通用等级
      'national_1': '国家级一等奖',
      'national_2': '国家级二等奖',
      'national_3': '国家级三等奖',
      'provincial_1': '省级一等奖',
      'provincial_2': '省级二等奖',
      'provincial_3': '省级三等奖',
      'school_1': '校级一等奖',
      'school_2': '校级二等奖',
      'school_3': '校级三等奖',
      'college_1': '院级一等奖',
      'college_2': '院级二等奖',
      'college_3': '院级三等奖',

      // 体育竞赛
      'national_other': '国家级其他名次（4-8名）',
      'provincial_other': '省级其他名次（4-8名）',
      'school_other': '校级其他名次（4-8名）',
      'college_other': '院级其他名次（不加分）',

      // 学生干部
      'school_president': '校级以上学生组织主席团成员',
      'college_president': '院学生会主席团成员/青协主席团成员',
      'grade_leader': '年级长（团支书）',
      'class_leader': '班长（团支书）',
      'department_head': '年级委员/班委成员',
      'society_president': '各社团主席团成员',
      'student_union_minister': '校院学生会部长',
      'society_minister': '各社团部长',
      'funding_captain': '院资助站队长（副队长）',
      'dormitory_head': '寝室长',
      'teaching_liaison': '教学联络员',

      // 社会实践志愿服务
      'national_individual': '国家级先进个人/优秀志愿者',
      'provincial_individual': '省级先进个人/优秀志愿者',
      'school_individual': '校级先进个人/优秀志愿者',
      'college_individual': '院级先进个人/优秀志愿者',
      'national_key_team': '国家级重点团队成员',
      'provincial_key_team': '省级重点团队成员',
      'school_key_team': '校级重点团队成员',
      'college_key_team': '院级重点团队成员',
      'national_normal_team': '国家级非重点团队成员',
      'provincial_normal_team': '省级非重点团队成员',
      'school_normal_team': '校级非重点团队成员',
      'college_normal_team': '院级非重点团队成员',

      // 专业认证
      'professional_high': '高级认证',
      'professional_mid': '中级认证',
      'professional_basic': '初级认证',
      'professional_low': '初级认证',

      // 科学研究
      'core_journal': '核心期刊论文',
      'patent_inventor': '发明专利发明人',
      'patent_participant': '发明专利参与者',

      // 英语六级
      'english_cet6': '通过六级考试',
      'cet6_excellent': '六级优秀(≥550分)',
      'cet6_good': '六级良好(500-549分)',
      'cet6_pass': '六级通过(425-499分)',

      // 见义勇为等表彰
      'national_honor': '国家级表彰',
      'provincial_honor': '省级表彰',
      'school_honor': '校级表彰',
      'national': '国家级表彰',
      'provincial': '省级表彰',
      'city': '市级表彰',
      'school': '校级表彰',

      // 新闻采编写
      'news_activity': '新闻采编写活动',

      'other': '其他'
    };
    return levelMap[level] || level;
  };

  const columns = [
    {
      title: '证书名称',
      dataIndex: 'certificate_name',
      key: 'certificate_name',
      ellipsis: true,
    },
    {
      title: '类别',
      dataIndex: 'category_name',
      key: 'category_name',
    },
    {
      title: '等级',
      dataIndex: 'level',
      key: 'level',
      render: (level) => getLevelText(level),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status),
    },
    {
      title: '得分',
      dataIndex: 'score',
      key: 'score',
      render: (score) => (score !== null && score !== undefined) ? `${score}分` : '-',
    },
    {
      title: '提交时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time) => {
        if (!time) return '-';
        try {
          return new Date(time).toLocaleString('zh-CN');
        } catch (error) {
          return '-';
        }
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          {record.status === 'pending' ? (
            <>
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              >
                编辑
              </Button>
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDelete(record.id)}
              >
                删除
              </Button>
            </>
          ) : (
            <Text type="secondary">已提交，无法修改</Text>
          )}
        </Space>
      ),
    },
  ];

  const uploadProps = {
    fileList,
    accept: 'image/*,.pdf',
    beforeUpload: (file) => {
      const allowedTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/bmp',
        'image/webp',
        'image/svg+xml',
        'application/pdf'
      ];

      if (!allowedTypes.includes(file.type)) {
        message.error('只能上传图片文件 (JPG, PNG, GIF, BMP, WebP, SVG) 或PDF文件！');
        return false;
      }
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error('文件大小不能超过5MB！');
        return false;
      }

      // 创建文件对象，确保包含 originFileObj
      const fileObj = {
        uid: file.uid || Date.now().toString(),
        name: file.name,
        status: 'done',
        originFileObj: file
      };

      setFileList([fileObj]);
      return false; // 阻止自动上传
    },
    onRemove: () => {
      setFileList([]);
    },
  };

  return (
    <div>
      {/* 系统公告 */}
      {systemSettings.announcement && (
        <Alert
          message="系统公告"
          description={systemSettings.announcement}
          type="info"
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 提交状态提示 */}
      {(() => {
        const submitCheck = canSubmitCertificate();
        if (!submitCheck.canSubmit) {
          return (
            <Alert
              message="证书提交限制"
              description={submitCheck.reason}
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />
          );
        }
        return null;
      })()}

      {/* 审核活动选择 */}
      <Card style={{ marginBottom: 16 }}>
        <div style={{ marginBottom: 16 }}>
          <Text strong style={{ fontSize: '16px' }}>选择审核活动</Text>
        </div>
        <Row gutter={16} align="top">
          <Col span={16}>
            <Select
              style={{ width: '100%' }}
              placeholder="请选择要参与的审核活动"
              value={selectedActivity}
              onChange={setSelectedActivity}
              allowClear

              styles={{
                popup: {
                  root: { maxHeight: 400, overflow: 'auto' }
                }
              }}
              size="large"
            >
              {auditActivities.map(activity => (
                <Option
                  key={activity.id}
                  value={activity.id}
                  label={activity.name}
                >
                  <div style={{ padding: '8px 0' }}>
                    <div style={{
                      fontWeight: 'bold',
                      marginBottom: '4px',
                      fontSize: '14px'
                    }}>
                      {activity.name}
                    </div>
                    <div style={{
                      fontSize: '12px',
                      color: '#666',
                      lineHeight: '1.4'
                    }}>
                      时间：{activity.start_date} 至 {activity.end_date}
                    </div>
                    {activity.description && (
                      <div style={{
                        fontSize: '12px',
                        color: '#999',
                        marginTop: '2px',
                        lineHeight: '1.4'
                      }}>
                        {activity.description}
                      </div>
                    )}
                  </div>
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={8}>
            {selectedActivity ? (
              <Alert
                message="已选择审核活动"
                description={auditActivities.find(a => a.id === selectedActivity)?.name}
                type="success"
                showIcon
                size="small"
              />
            ) : (
              <Alert
                message="请先选择审核活动"
                description="选择审核活动后才能提交证书"
                type="warning"
                showIcon
                size="small"
              />
            )}
          </Col>
        </Row>
      </Card>

      {/* 显示各类别提交状态 - 基于选定的审核活动 */}
      {selectedActivity && (() => {
        const limitedCategories = ['学生干部', '体育竞赛', '文艺活动', '科学研究'];
        const statusInfo = limitedCategories.map(categoryName => {
          const limit = getCategorySubmissionLimit(categoryName);
          const submitted = getSubmittedCertificateCount(categoryName, selectedActivity);
          return { categoryName, limit, submitted, remaining: limit - submitted };
        }).filter(info => info.limit); // 只显示有限制的类别

        if (statusInfo.length > 0) {
          const selectedActivityName = auditActivities.find(a => a.id === selectedActivity)?.name;
          return (
            <Card style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>
                <Text strong>证书提交状态</Text>
                <Text style={{ marginLeft: 8, fontSize: '12px', color: '#666' }}>
                  （当前活动：{selectedActivityName}）
                </Text>
              </div>
              <Row gutter={16}>
                {statusInfo.map(info => (
                  <Col key={info.categoryName} span={6}>
                    <div style={{
                      padding: 8,
                      backgroundColor: info.remaining > 0 ? '#f6ffed' : '#fff2f0',
                      border: `1px solid ${info.remaining > 0 ? '#b7eb8f' : '#ffccc7'}`,
                      borderRadius: 4,
                      textAlign: 'center'
                    }}>
                      <div style={{ fontSize: '12px', color: '#666' }}>{info.categoryName}</div>
                      <div style={{
                        fontSize: '14px',
                        fontWeight: 'bold',
                        color: info.remaining > 0 ? '#52c41a' : '#ff4d4f'
                      }}>
                        {info.submitted}/{info.limit}
                      </div>
                      <div style={{ fontSize: '10px', color: '#999' }}>
                        {info.remaining > 0 ? `还可提交${info.remaining}个` : '已达上限'}
                      </div>
                    </div>
                  </Col>
                ))}
              </Row>
            </Card>
          );
        }
        return null;
      })()}

      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={2}>
            <FileTextOutlined /> 证书管理
          </Title>
        </Col>
        <Col>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              disabled={!canSubmitCertificate().canSubmit || !selectedActivity}
              onClick={() => {
                if (!selectedActivity) {
                  message.warning('请先选择审核活动');
                  return;
                }

                const submitCheck = canSubmitCertificate();
                if (!submitCheck.canSubmit) {
                  message.warning(submitCheck.reason);
                  return;
                }

                if (!hasAvailableCategories()) {
                  const activityName = auditActivities.find(a => a.id === selectedActivity)?.name;
                  message.warning(`在当前审核活动"${activityName}"中，所有有限制的证书类别都已达到提交上限，无法继续提交`);
                  return;
                }

                setEditingCertificate(null);
                form.resetFields();
                setFileList([]);
                setModalVisible(true);
              }}
            >
              提交证书
            </Button>
            <Button
              type="default"
              icon={<PlusOutlined />}
              disabled={!canSubmitCertificate().canSubmit || !selectedActivity}
              onClick={() => {
                if (!selectedActivity) {
                  message.warning('请先选择审核活动');
                  return;
                }

                const submitCheck = canSubmitCertificate();
                if (!submitCheck.canSubmit) {
                  message.warning(submitCheck.reason);
                  return;
                }
                setBatchCertificates([{
                  key: 1,
                  category_id: null,
                  certificate_name: '',
                  level: '',
                  award_date: '',
                  team_size: 1,
                  role: 'individual',
                  files: []
                }]);
                setFileList([]);
                setBatchModalVisible(true);
              }}
            >
              批量提交
            </Button>
          </Space>
        </Col>
      </Row>

      <Card>
        {selectedActivity ? (
          <Table
            columns={columns}
            dataSource={getCurrentActivityCertificates()}
            rowKey="id"
            loading={loading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
            }}
          />
        ) : (
          <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
            <p>请先选择审核活动查看相关证书</p>
          </div>
        )}
      </Card>

      <Modal
        title={editingCertificate ? '编辑证书' : '提交证书'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
          setFileList([]);
          setEditingCertificate(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="category_id"
            label="证书类别"
            rules={[{ required: true, message: '请选择证书类别' }]}
          >
            <Select
              placeholder="请选择证书类别"
              allowClear
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              onChange={(value) => {
                const category = getCurrentCategory(value);
                setSelectedCategory(category);

                // 英语六级特殊处理
                if (category && category.category_name === '英语六级') {
                  form.setFieldsValue({
                    certificate_name: '全国大学英语六级考试',
                    level: 'pass', // 默认为通过
                    team_size: 1,
                    role: 'individual'
                  });
                } else {
                  // 重置相关字段
                  form.setFieldsValue({
                    certificate_name: '',
                    level: '',
                    team_size: 1,
                    role: 'individual'
                  });
                }
              }}
            >
              {categories && categories.length > 0 ? categories.map(category => {
                const limit = getCategorySubmissionLimit(category.category_name);
                const submitted = getSubmittedCertificateCount(category.category_name, selectedActivity);
                const isDisabled = limit && submitted >= limit;

                return (
                  <Option key={category.id} value={category.id} disabled={isDisabled}>
                    {category.category_name}
                    {isDisabled && ' (已达上限)'}
                  </Option>
                );
              }) : (
                <Option disabled value="">
                  暂无证书类别
                </Option>
              )}
            </Select>
          </Form.Item>

          {/* 显示类别提交限制提示 */}
          {selectedCategory && (() => {
            const limit = getCategorySubmissionLimit(selectedCategory.category_name);
            if (!limit) return null;

            const alreadySubmittedCount = getSubmittedCertificateCount(selectedCategory.category_name, selectedActivity);
            const canSubmit = alreadySubmittedCount < limit;

            return (
              <div style={{
                marginBottom: 16,
                padding: 8,
                backgroundColor: canSubmit ? '#f6ffed' : '#fff2f0',
                border: `1px solid ${canSubmit ? '#b7eb8f' : '#ffccc7'}`,
                borderRadius: 4
              }}>
                <Text style={{
                  fontSize: '12px',
                  color: canSubmit ? '#52c41a' : '#ff4d4f'
                }}>
                  {selectedCategory.category_name}类别限制：最多提交{limit}个证书
                  {alreadySubmittedCount > 0 && ` (已提交${alreadySubmittedCount}个)`}
                  {!canSubmit && ` - 已达到提交上限！`}
                </Text>
              </div>
            );
          })()}

          {/* 动态显示证书名称字段 */}
          {selectedCategory && (() => {
            // 英语六级特殊处理：显示固定的证书名称，不可编辑
            if (selectedCategory.category_name === '英语六级') {
              return (
                <Form.Item
                  name="certificate_name"
                  label="考试名称"
                >
                  <Input
                    value="全国大学英语六级考试"
                    disabled
                    style={{ backgroundColor: '#f5f5f5' }}
                  />
                </Form.Item>
              );
            }

            // 如果使用预定义名称，显示下拉选择（支持自定义输入）
            if (selectedCategory.use_predefined_names && selectedCategory.predefined_names) {
              const fieldLabel = selectedCategory.category_name === '学科竞赛' ? '竞赛名称' :
                               selectedCategory.category_name === '文艺活动' ? '活动名称' : '证书名称';

              return (
                <Form.Item
                  name="certificate_name"
                  label={fieldLabel}
                  rules={[{ required: true, message: `请选择或输入${fieldLabel}` }]}
                >
                  {selectedCategory.allow_custom_input ? (
                    <AutoComplete
                      placeholder={`请选择或输入${fieldLabel}`}
                      allowClear
                      filterOption={(inputValue, option) =>
                        option?.value?.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
                      }
                      options={selectedCategory.predefined_names.map(name => ({
                        value: name,
                        label: name
                      }))}
                    />
                  ) : (
                    <Select
                      placeholder={`请选择${fieldLabel}`}
                      showSearch
                      allowClear
                      filterOption={(input, option) =>
                        option?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                    >
                      {selectedCategory.predefined_names.map(name => (
                        <Option key={name} value={name}>
                          {name}
                        </Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              );
            }

            // 如果需要证书名称但不使用预定义名称，显示输入框
            if (needsCertificateName(selectedCategory.category_name)) {
              return (
                <Form.Item
                  name="certificate_name"
                  label={selectedCategory.category_name === '科学研究' ? '论文/专利名称' : '证书名称'}
                  rules={[{ required: true, message: `请输入${selectedCategory.category_name === '科学研究' ? '论文/专利名称' : '证书名称'}` }]}
                >
                  <Input placeholder={`请输入${selectedCategory.category_name === '科学研究' ? '论文/专利名称' : '证书名称'}`} />
                </Form.Item>
              );
            }

            return null;
          })()}

          <Form.Item
            name="level"
            label={selectedCategory?.category_name === '学生干部' ? '职务等级' :
                   selectedCategory?.category_name === '专业认证' ? '认证等级' :
                   selectedCategory?.category_name === '英语六级' ? '考试类型' :
                   selectedCategory?.category_name === '科学研究' ? '成果类型' :
                   selectedCategory?.category_name === '新闻采编写' ? '活动类型' : '获奖等级'}
            rules={[{ required: true, message: '请选择等级' }]}
          >
            <Select placeholder="请选择等级">
              {selectedCategory && getLevelOptions(selectedCategory.category_name).map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* 动态显示获奖日期 */}
          <Form.Item
            name="award_date"
            label={selectedCategory?.category_name === '科学研究' ? '发表/授权日期' :
                   selectedCategory?.category_name === '学生干部' ? '任职日期' :
                   selectedCategory?.category_name === '新闻采编写' ? '活动日期' : '获奖日期'}
          >
            <Input
              type="date"
              placeholder="请选择日期"
              style={{ width: '100%' }}
            />
          </Form.Item>

          {/* 动态显示团队相关字段 */}
          {selectedCategory && needsTeamFields(selectedCategory.category_name) && (
            <>
              <Form.Item
                name="team_size"
                label="团队人数"
                initialValue={1}
              >
                <Select>
                  <Option value={1}>个人</Option>
                  <Option value={2}>2人团队</Option>
                  <Option value={3}>3人团队</Option>
                  <Option value={4}>4人及以上团队</Option>
                </Select>
              </Form.Item>
            </>
          )}

          {/* 动态显示角色字段（学生创新型项目） */}
          {selectedCategory && needsRoleField(selectedCategory.category_name) && (
            <Form.Item
              name="role"
              label="项目角色"
              initialValue="member"
            >
              <Select>
                <Option value="leader">主持人</Option>
                <Option value="member">参与成员</Option>
              </Select>
            </Form.Item>
          )}

          <Form.Item
            label="证书文件"
            extra="支持上传图片文件 (JPG, PNG, GIF, BMP, WebP, SVG) 或PDF文件，大小不超过5MB"
          >
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />}>选择文件</Button>
            </Upload>
          </Form.Item>

          <Divider />

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingCertificate ? '更新' : '提交'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量提交证书Modal */}
      <Modal
        title="批量提交证书"
        open={batchModalVisible}
        onCancel={() => setBatchModalVisible(false)}
        width={800}
        footer={null}
      >
        <div>
          <div style={{ marginBottom: 16 }}>
            <Text type="secondary">
              您可以一次性提交多个证书，每个证书都需要填写完整的信息。
            </Text>
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              <Text type="secondary">
                提交数量限制：学生干部（最多1个）、体育竞赛（最多2个）、文艺活动（最多2个）、科学研究（最多2个）
              </Text>
            </div>
          </div>

          {batchCertificates.map((cert, index) => {
            // 获取当前证书的最新状态
            const currentCert = batchCertificates.find(c => c.key === cert.key) || cert;

            return (
            <Card
              key={`cert-${cert.key}`}
              size="small"
              title={`证书 ${index + 1}`}
              style={{ marginBottom: 16 }}
              extra={
                batchCertificates.length > 1 && (
                  <Button
                    type="link"
                    danger
                    size="small"
                    onClick={() => removeBatchCertificate(cert.key)}
                  >
                    删除
                  </Button>
                )
              }
            >
              {/* 证书类别选择 */}
              <div style={{ marginBottom: 16 }}>
                <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
                  证书类别 <span style={{ color: 'red' }}>*</span>
                </label>
                <Select
                  key={`category-select-${cert.key}`}
                  placeholder="请选择证书类别"
                  value={cert.category_id || undefined}
                  style={{ width: '100%' }}
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                  onChange={(value) => {
                    updateBatchCertificate(cert.key, 'category_id', value);

                    const category = getCurrentCategory(value);
                    // 英语六级特殊处理
                    if (category && category.category_name === '英语六级') {
                      updateBatchCertificate(cert.key, 'certificate_name', '全国大学英语六级考试');
                      updateBatchCertificate(cert.key, 'level', 'pass');
                      updateBatchCertificate(cert.key, 'team_size', 1);
                      updateBatchCertificate(cert.key, 'role', 'individual');
                    } else {
                      // 重置相关字段
                      updateBatchCertificate(cert.key, 'certificate_name', '');
                      updateBatchCertificate(cert.key, 'level', '');
                      updateBatchCertificate(cert.key, 'team_size', 1);
                      updateBatchCertificate(cert.key, 'role', 'individual');
                    }
                  }}
                >
                  {categories && categories.length > 0 ? categories.map(category => {
                    const limit = getCategorySubmissionLimit(category.category_name);
                    const submitted = getSubmittedCertificateCount(category.category_name, selectedActivity);
                    const isDisabled = limit && submitted >= limit;

                    return (
                      <Option key={category.id} value={category.id} disabled={isDisabled}>
                        {category.category_name}
                        {isDisabled && ' (已达上限)'}
                      </Option>
                    );
                  }) : (
                    <Option disabled value="">
                      暂无证书类别
                    </Option>
                  )}
                </Select>
              </div>

              {/* 显示类别提交限制提示 */}
              {currentCert.category_id && (() => {
                const category = getCurrentCategory(currentCert.category_id);
                if (!category) return null;

                const limit = getCategorySubmissionLimit(category.category_name);
                if (!limit) return null;

                // 统计当前已选择的同类别证书数量
                const sameCategories = batchCertificates.filter(cert =>
                  cert.category_id === currentCert.category_id
                );
                const currentCount = sameCategories.length;
                const alreadySubmittedCount = getSubmittedCertificateCount(category.category_name, selectedActivity);
                const totalCount = currentCount + alreadySubmittedCount;

                return (
                  <div style={{
                    marginBottom: 16,
                    padding: 8,
                    backgroundColor: totalCount > limit ? '#fff2f0' : '#f6ffed',
                    border: `1px solid ${totalCount > limit ? '#ffccc7' : '#b7eb8f'}`,
                    borderRadius: 4
                  }}>
                    <Text style={{
                      fontSize: '12px',
                      color: totalCount > limit ? '#ff4d4f' : '#52c41a'
                    }}>
                      {category.category_name}类别限制：最多提交{limit}个证书
                      {alreadySubmittedCount > 0 && ` (已提交${alreadySubmittedCount}个)`}
                      {currentCount > 0 && ` (本次选择${currentCount}个)`}
                      {totalCount > limit && ` - 超出限制！`}
                    </Text>
                  </div>
                );
              })()}

              {/* 动态显示证书名称字段 */}
              {currentCert.category_id && (() => {
                const category = getCurrentCategory(currentCert.category_id);
                if (!category) return null;

                // 英语六级特殊处理：显示固定的证书名称，不可编辑
                if (category.category_name === '英语六级') {
                  return (
                    <div style={{ marginBottom: 16 }}>
                      <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
                        考试名称
                      </label>
                      <Input
                        value="全国大学英语六级考试"
                        disabled
                        style={{ backgroundColor: '#f5f5f5' }}
                      />
                    </div>
                  );
                }

                // 如果使用预定义名称，显示下拉选择（支持自定义输入）
                if (category.use_predefined_names && category.predefined_names) {
                  const fieldLabel = category.category_name === '学科竞赛' ? '竞赛名称' :
                                   category.category_name === '文艺活动' ? '活动名称' : '证书名称';

                  return (
                    <div style={{ marginBottom: 16 }}>
                      <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
                        {fieldLabel} <span style={{ color: 'red' }}>*</span>
                      </label>
                      {category.allow_custom_input ? (
                        <AutoComplete
                          placeholder={`请选择或输入${fieldLabel}`}
                          value={currentCert.certificate_name}
                          style={{ width: '100%' }}
                          onChange={(value) => updateBatchCertificate(currentCert.key, 'certificate_name', value)}
                          allowClear
                          filterOption={(inputValue, option) =>
                            option?.value?.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
                          }
                          options={category.predefined_names.map(name => ({
                            value: name,
                            label: name
                          }))}
                        />
                      ) : (
                        <Select
                          placeholder={`请选择${fieldLabel}`}
                          value={currentCert.certificate_name}
                          style={{ width: '100%' }}
                          onChange={(value) => updateBatchCertificate(currentCert.key, 'certificate_name', value)}
                          showSearch
                          allowClear
                          filterOption={(input, option) =>
                            option?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                          }
                        >
                          {category.predefined_names.map(name => (
                            <Option key={name} value={name}>
                              {name}
                            </Option>
                          ))}
                        </Select>
                      )}
                    </div>
                  );
                }

                // 如果需要证书名称但不使用预定义名称，显示输入框
                if (needsCertificateName(category.category_name)) {
                  return (
                    <div style={{ marginBottom: 16 }}>
                      <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
                        {category.category_name === '科学研究' ? '论文/专利名称' : '证书名称'} <span style={{ color: 'red' }}>*</span>
                      </label>
                      <Input
                        placeholder={`请输入${category.category_name === '科学研究' ? '论文/专利名称' : '证书名称'}`}
                        value={currentCert.certificate_name}
                        onChange={(e) => updateBatchCertificate(currentCert.key, 'certificate_name', e.target.value)}
                      />
                    </div>
                  );
                }

                return null;
              })()}
              {/* 动态显示获奖等级字段 */}
              {currentCert.category_id && (() => {
                const category = getCurrentCategory(currentCert.category_id);
                if (!category) return null;

                return (
                  <div style={{ marginBottom: 16 }}>
                    <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
                      获奖等级 <span style={{ color: 'red' }}>*</span>
                    </label>
                    <Select
                      placeholder="请选择获奖等级"
                      value={currentCert.level}
                      style={{ width: '100%' }}
                      onChange={(value) => updateBatchCertificate(currentCert.key, 'level', value)}
                    >
                      {getLevelOptions(category.category_name).map(option => (
                        <Option key={option.value} value={option.value}>
                          {option.label}
                        </Option>
                      ))}
                    </Select>
                  </div>
                );
              })()}

              {/* 获奖日期字段 */}
              <div style={{ marginBottom: 16 }}>
                <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
                  获奖日期
                </label>
                <Input
                  type="date"
                  value={currentCert.award_date}
                  onChange={(e) => updateBatchCertificate(currentCert.key, 'award_date', e.target.value)}
                />
              </div>
              {/* 动态显示团队相关字段 */}
              {currentCert.category_id && (() => {
                const category = getCurrentCategory(currentCert.category_id);
                if (!needsTeamFields(category?.category_name)) return null;

                return (
                  <>
                    <div style={{ marginBottom: 16 }}>
                      <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
                        团队人数
                      </label>
                      <Select
                        placeholder="请选择团队人数"
                        value={currentCert.team_size}
                        style={{ width: '100%' }}
                        onChange={(value) => updateBatchCertificate(currentCert.key, 'team_size', value)}
                      >
                        <Option value={1}>个人</Option>
                        <Option value={2}>2人团队</Option>
                        <Option value={3}>3人团队</Option>
                        <Option value={4}>4人及以上团队</Option>
                      </Select>
                    </div>
                  </>
                );
              })()}

              {/* 动态显示角色字段（学生创新型项目） */}
              {currentCert.category_id && (() => {
                const category = getCurrentCategory(currentCert.category_id);
                if (!needsRoleField(category?.category_name)) return null;

                return (
                  <div style={{ marginBottom: 16 }}>
                    <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
                      项目角色
                    </label>
                    <Select
                      placeholder="请选择项目角色"
                      value={currentCert.role}
                      style={{ width: '100%' }}
                      onChange={(value) => updateBatchCertificate(currentCert.key, 'role', value)}
                    >
                      <Option value="leader">负责人</Option>
                      <Option value="member">参与者</Option>
                    </Select>
                  </div>
                );
              })()}

              {/* 每个证书的独立文件上传 */}
              <div style={{ marginBottom: 16 }}>
                <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
                  证书文件 <span style={{ color: 'red' }}>*</span>
                </label>
                <div style={{ fontSize: '12px', color: '#666', marginBottom: 8 }}>
                  支持上传图片文件 (JPG, PNG, GIF, BMP, WebP, SVG) 或PDF文件，大小不超过5MB
                </div>
                    <Upload
                      fileList={currentCert.files || []}
                      accept="image/*,.pdf"
                      beforeUpload={(file) => {
                        const allowedTypes = [
                          'image/jpeg',
                          'image/jpg',
                          'image/png',
                          'image/gif',
                          'image/bmp',
                          'image/webp',
                          'image/svg+xml',
                          'application/pdf'
                        ];

                        if (!allowedTypes.includes(file.type)) {
                          message.error('只能上传图片文件 (JPG, PNG, GIF, BMP, WebP, SVG) 或PDF文件！');
                          return false;
                        }

                        const isLt5M = file.size / 1024 / 1024 < 5;
                        if (!isLt5M) {
                          message.error('文件大小不能超过5MB！');
                          return false;
                        }

                        // 创建文件对象，确保包含 originFileObj
                        const fileObj = {
                          uid: file.uid || Date.now().toString(),
                          name: file.name,
                          status: 'done',
                          originFileObj: file
                        };

                        const newFiles = [...(currentCert.files || []), fileObj];
                        updateBatchCertificateFiles(currentCert.key, newFiles);
                        return false;
                      }}
                      onRemove={(file) => {
                        const newFiles = (currentCert.files || []).filter(f => f.uid !== file.uid);
                        updateBatchCertificateFiles(currentCert.key, newFiles);
                      }}
                      multiple
                    >
                      <Button icon={<UploadOutlined />}>选择文件</Button>
                    </Upload>
                  </div>

              {/* 删除证书按钮 */}
              {batchCertificates.length > 1 && (
                <div style={{ textAlign: 'right', marginTop: 16 }}>
                  <Button
                    type="link"
                    danger
                    onClick={() => removeBatchCertificate(currentCert.key)}
                  >
                    删除此证书
                  </Button>
                </div>
              )}
            </Card>
            );
          })}

          <div style={{ textAlign: 'center', marginBottom: 16 }}>
            {(() => {
              // 检查是否有类别已达到限制
              const categoryCount = {};
              batchCertificates.forEach(cert => {
                if (cert.category_id) {
                  const category = getCurrentCategory(cert.category_id);
                  if (category) {
                    const categoryName = category.category_name;
                    categoryCount[categoryName] = (categoryCount[categoryName] || 0) + 1;
                  }
                }
              });

              // 检查是否有限制类别已满
              const hasLimitReached = Object.keys(categoryCount).some(categoryName => {
                const limit = getCategorySubmissionLimit(categoryName);
                return limit && categoryCount[categoryName] >= limit;
              });

              const limitedCategories = Object.keys(categoryCount)
                .filter(categoryName => {
                  const limit = getCategorySubmissionLimit(categoryName);
                  return limit && categoryCount[categoryName] >= limit;
                })
                .join('、');

              return (
                <>
                  <Button
                    type="dashed"
                    icon={<PlusOutlined />}
                    onClick={addBatchCertificate}
                    style={{ width: '100%' }}
                  >
                    添加更多证书
                  </Button>
                  {hasLimitReached && (
                    <div style={{ marginTop: 8, fontSize: '12px', color: '#ff4d4f' }}>
                      注意：{limitedCategories} 已达到提交数量限制
                    </div>
                  )}
                </>
              );
            })()}
          </div>

          <Divider />

          <div style={{ marginBottom: 0, textAlign: 'right', marginTop: 16 }}>
            <Space>
              <Button onClick={() => setBatchModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                loading={loading}
                onClick={() => handleBatchSubmit({})}
              >
                批量提交
              </Button>
            </Space>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CertificateManagement;
