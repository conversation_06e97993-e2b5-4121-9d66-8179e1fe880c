services:
  - type: web
    name: student-assessment-system
    env: node
    plan: free
    buildCommand: chmod +x build.sh && ./build.sh && cd server && npm install
    startCommand: cd server && npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: JWT_SECRET
        generateValue: true
      - key: ENABLE_RATE_LIMIT
        value: false
      - key: ENABLE_SECURITY_CHECKS
        value: false
      - key: STRICT_MODE
        value: false
      - key: ENABLE_ADMIN_DETAILED_LOGS
        value: false
      - key: SUPABASE_URL
        value: https://gjajmzkzzunwtlanrmox.supabase.co
      - key: SUPABASE_ANON_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.VbmQ20N8Q3hEAhleIoauk2LMvQ2ZFwsFRrTNHscqGAo
