module.exports = async (req, res) => {
  // 设置 CORS 头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: '方法不允许' });
  }

  res.status(200).json({
    success: true,
    message: 'API 测试成功',
    data: {
      timestamp: new Date().toISOString(),
      environment: 'Vercel Functions',
      nodeVersion: process.version,
      method: req.method,
      url: req.url
    }
  });
};
