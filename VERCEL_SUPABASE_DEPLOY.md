# 🚀 Vercel + Supabase 部署指南

## 📋 部署概述

本项目使用 **Vercel + Supabase** 的完全免费部署方案：
- **Vercel**: 前端托管 + Serverless Functions API
- **Supabase**: PostgreSQL 数据库 + 认证服务

## 🎯 部署步骤

### 第一步：设置 Supabase 数据库

1. **创建 Supabase 项目**
   - 访问 [supabase.com](https://supabase.com)
   - 点击 "Start your project"
   - 使用 GitHub 账号登录
   - 创建新项目（选择免费计划）

2. **执行数据库脚本**
   - 在 Supabase Dashboard 中，进入 "SQL Editor"
   - 复制 `supabase-setup.sql` 文件内容
   - 粘贴并执行脚本
   - 确认所有表和数据都创建成功

3. **获取数据库连接信息**
   - 在 Supabase Dashboard 中，进入 "Settings" > "API"
   - 记录以下信息：
     - `Project URL`
     - `anon public key`

### 第二步：部署到 Vercel

1. **准备 GitHub 仓库**
   ```bash
   git add .
   git commit -m "准备 Vercel + Supabase 部署"
   git push origin main
   ```

2. **连接 Vercel**
   - 访问 [vercel.com](https://vercel.com)
   - 使用 GitHub 账号登录
   - 点击 "New Project"
   - 选择您的 GitHub 仓库
   - 点击 "Import"

3. **配置环境变量**
   在 Vercel 项目设置中添加以下环境变量：
   ```
   SUPABASE_URL=your_supabase_project_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   JWT_SECRET=your_jwt_secret_key
   NODE_ENV=production
   ```

4. **部署项目**
   - Vercel 会自动检测配置并开始部署
   - 等待部署完成（通常 2-3 分钟）
   - 获取部署 URL

### 第三步：测试部署

1. **访问应用**
   - 打开 Vercel 提供的 URL
   - 测试登录功能（用户名: admin, 密码: password）

2. **测试 API**
   ```bash
   curl https://your-app.vercel.app/api/health
   ```

## 🔧 配置说明

### 项目结构
```
├── api/                    # Vercel Functions
│   ├── _middleware.js      # 中间件和工具函数
│   ├── _supabase.js       # Supabase 客户端
│   ├── auth/              # 认证相关 API
│   ├── certificates/      # 证书相关 API
│   └── package.json       # API 依赖
├── client/                # React 前端
├── vercel.json           # Vercel 配置
└── supabase-setup.sql    # 数据库初始化脚本
```

### 环境变量说明
- `SUPABASE_URL`: Supabase 项目 URL
- `SUPABASE_ANON_KEY`: Supabase 匿名访问密钥
- `JWT_SECRET`: JWT 令牌签名密钥（建议使用随机字符串）
- `NODE_ENV`: 运行环境（production）

## 🎉 优势特点

### ✅ 完全免费
- **Vercel**: 每月 100GB 带宽，无限部署
- **Supabase**: 500MB 数据库，50MB 文件存储

### ✅ 高性能
- **CDN 加速**: Vercel 全球 CDN
- **Serverless**: 按需扩展，零冷启动
- **PostgreSQL**: 高性能关系型数据库

### ✅ 易于维护
- **自动部署**: Git 推送自动触发部署
- **实时监控**: Vercel 提供详细的性能监控
- **备份恢复**: Supabase 自动备份

## 🔍 故障排除

### 常见问题

1. **API 调用失败**
   - 检查环境变量是否正确设置
   - 确认 Supabase 数据库连接正常

2. **数据库连接错误**
   - 验证 SUPABASE_URL 和 SUPABASE_ANON_KEY
   - 检查 Supabase 项目是否处于活跃状态

3. **部署失败**
   - 检查 vercel.json 配置
   - 确认所有依赖都已正确安装

### 调试方法
```bash
# 查看 Vercel 部署日志
vercel logs your-deployment-url

# 测试 API 端点
curl -X GET https://your-app.vercel.app/api/health
```

## 📞 技术支持

如果遇到部署问题，请检查：
1. GitHub 仓库是否包含所有必要文件
2. Supabase 数据库是否正确初始化
3. Vercel 环境变量是否正确配置

## 🎯 下一步

部署成功后，您可以：
1. 自定义域名绑定
2. 配置 SSL 证书（Vercel 自动提供）
3. 设置监控和告警
4. 优化性能和 SEO
