import {
  db,
  authenticateToken,
  handleCors,
  handleError,
  handleSuccess
} from '../_middleware.js';

export default async function handler(req, res) {
  // 处理 CORS
  if (handleCors(req, res)) return;

  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: '方法不允许' });
  }

  try {
    // 验证令牌
    const { user } = authenticateToken(req);

    // 获取所有学生
    const students = await db.getUsers({ role: 'student' });

    // 统计班级信息
    const classes = {};
    students.forEach(student => {
      if (student.class_name) {
        if (!classes[student.class_name]) {
          classes[student.class_name] = {
            name: student.class_name,
            student_count: 0
          };
        }
        classes[student.class_name].student_count++;
      }
    });

    // 转换为数组并排序
    const classList = Object.values(classes).sort((a, b) => 
      a.name.localeCompare(b.name)
    );

    handleSuccess(res, classList, '获取班级列表成功');

  } catch (error) {
    handleError(res, error, 401);
  }
}
