{"version": 2, "buildCommand": "cd client && npm run build", "outputDirectory": "client/dist", "installCommand": "cd client && npm install && cd ../api && npm install", "functions": {"api/**/*.js": {"runtime": "nodejs18.x"}}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}], "env": {"NODE_ENV": "production", "SUPABASE_URL": "https://gjajmzkzzunwtlanrmox.supabase.co", "SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.VbmQ20N8Q3hEAhleIoauk2LMvQ2ZFwsFRrTNHscqGAo"}}