{"name": "student-assessment-system", "version": "1.0.0", "description": "学生素质测评系统 - 全栈应用", "main": "server/sqlite-server.js", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "scripts": {"start": "cd server && npm start", "build": "chmod +x build.sh && ./build.sh", "install-deps": "cd server && npm install && cd ../client && npm install", "dev": "npm run install-deps && npm run build && npm start"}, "keywords": ["student", "assessment", "certificate", "management", "react", "nodejs", "express"], "author": "Student Assessment System", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ruiyan886/henurjxylyhsanping.git"}, "dependencies": {"@supabase/supabase-js": "^2.50.3"}}