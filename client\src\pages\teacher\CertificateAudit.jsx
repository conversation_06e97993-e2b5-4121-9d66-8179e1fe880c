import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  message, 
  Space,
  Typography,
  Row,
  Col,
  Tag,
  Image,
  Divider,
  InputNumber,
  Radio,
  Popconfirm
} from 'antd';
import { 
  CheckCircleOutlined,
  CloseCircleOutlined,
  EyeOutlined,
  EditOutlined,
  FileTextOutlined,
  SearchOutlined,
  ReloadOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { certificateAPI, categoryAPI, auditActivityAPI } from '../../utils/api';

const { Title, Text } = Typography;
const { Option } = Select;
const { Search } = Input;
const { TextArea } = Input;

const CertificateAudit = () => {
  const [loading, setLoading] = useState(false);
  const [certificates, setCertificates] = useState([]);
  const [categories, setCategories] = useState([]);
  const [auditActivities, setAuditActivities] = useState([]);
  const [selectedActivity, setSelectedActivity] = useState(null);
  const [auditModalVisible, setAuditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentCertificate, setCurrentCertificate] = useState(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [autoNext, setAutoNext] = useState(true);
  const [editMode, setEditMode] = useState(false); // 编辑模式状态
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [auditForm] = Form.useForm(); // 审核表单
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  useEffect(() => {
    fetchCategories();
    fetchAuditActivities();
  }, []);

  // 当选择的审核活动变化时，重新获取证书
  useEffect(() => {
    if (selectedActivity !== null) {
      fetchCertificates();
    }
  }, [selectedActivity]);

  const fetchCertificates = async (params = {}) => {
    try {
      setLoading(true);

      const response = await certificateAPI.getPendingCertificates({
        page: pagination.current,
        limit: pagination.pageSize,
        search: searchText,
        status: statusFilter,
        category_id: categoryFilter,
        audit_activity_id: selectedActivity || '', // 确保传递空字符串而不是null
        ...params
      });

      const certificateList = response.data.certificates || [];
      setCertificates(certificateList);
      setPagination(prev => ({
        ...prev,
        total: response.data.total || 0
      }));
    } catch (error) {
      console.error('获取证书列表失败:', error);
      message.error('获取证书列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await categoryAPI.getCategories();
      setCategories(response.data.flat_categories || []);
    } catch (error) {
      console.error('获取类别列表失败:', error);
    }
  };

  const fetchAuditActivities = async () => {
    try {
      const response = await auditActivityAPI.getActivities();
      const activities = response.data.activities || [];
      setAuditActivities(activities);

      // 如果还没有选择审核活动，默认选择第一个
      if (selectedActivity === null && activities.length > 0) {
        setSelectedActivity(activities[0].id);
      }
    } catch (error) {
      console.error('获取审核活动失败:', error);
    }
  };

  // 处理证书信息修改
  const handleCertificateEdit = async (values) => {
    try {
      await certificateAPI.updateCertificate(currentCertificate.id, values);
      message.success('证书信息修改成功');

      // 更新当前证书信息
      const updatedCertificate = { ...currentCertificate, ...values };
      setCurrentCertificate(updatedCertificate);

      // 重新计算默认分数
      const newScore = getDefaultScore(values.category_id || currentCertificate.category_id,
                                      values.level || currentCertificate.level,
                                      values.team_size || currentCertificate.team_size,
                                      values.role || currentCertificate.role);

      // 更新审核表单的分数
      auditForm.setFieldsValue({ score: newScore });

      setEditMode(false);
      fetchCertificates(); // 刷新列表
    } catch (error) {
      console.error('修改证书信息失败:', error);
      message.error('修改证书信息失败: ' + (error.response?.data?.message || error.message));
    }
  };

  const handleAudit = async (values) => {
    try {
      await certificateAPI.auditCertificate(currentCertificate.id, values);
      message.success('审核完成');
      setAuditModalVisible(false);
      auditForm.resetFields();
      setEditMode(false);

      // 自动跳转到下一个待审核证书
      if (autoNext) {
        const nextCertificate = getNextPendingCertificate();
        if (nextCertificate) {
          setCurrentCertificate(nextCertificate);
          setCurrentIndex(certificates.findIndex(cert => cert.id === nextCertificate.id));
          // 重新打开审核模态框
          auditForm.setFieldsValue({
            action: 'approve',
            score: getDefaultScore(nextCertificate.category_id, nextCertificate.level, nextCertificate.team_size, nextCertificate.role),
            comment: ''
          });
          setAuditModalVisible(true);
        } else {
          setCurrentCertificate(null);
          message.info('所有证书审核完成');
        }
      } else {
        setCurrentCertificate(null);
      }

      fetchCertificates();
    } catch (error) {
      console.error('审核失败:', error);
      message.error('审核失败: ' + (error.response?.data?.message || error.message));
    }
  };

  const handleQuickApprove = async (record) => {
    try {
      await certificateAPI.auditCertificate(record.id, {
        action: 'approve',
        score: getDefaultScore(record.category_id, record.level, record.team_size, record.role),
        comment: '审核通过'
      });
      message.success('审核通过');
      fetchCertificates();
    } catch (error) {
      console.error('审核失败:', error);
      message.error('审核失败');
    }
  };

  const handleQuickReject = async (record) => {
    try {
      await certificateAPI.auditCertificate(record.id, {
        action: 'reject',
        score: 0,
        comment: '审核不通过'
      });
      message.success('已拒绝');
      fetchCertificates();
    } catch (error) {
      console.error('操作失败:', error);
      message.error('操作失败');
    }
  };

  const getDefaultScore = (categoryId, level, teamSize = 1, role = 'individual') => {
    // 根据类别和等级返回默认分数（严格按照细则计算）
    let baseScore = 0;

    // 根据类别ID获取类别名称
    const category = categories.find(cat => cat.id === categoryId);
    if (!category) return 0;

    switch (category.category_name) {
      case '学科竞赛':
        const academicScores = {
          'national_1': 12, 'national_2': 10, 'national_3': 8,
          'provincial_1': 8, 'provincial_2': 7, 'provincial_3': 6,
          'school_1': 6, 'school_2': 5, 'school_3': 4,
          'college_1': 4, 'college_2': 3, 'college_3': 2
        };
        baseScore = academicScores[level] || 0;
        break;

      case '实践技能类竞赛':
        const practicalScores = {
          'national_1': 10, 'national_2': 9, 'national_3': 8,
          'provincial_1': 8, 'provincial_2': 7, 'provincial_3': 6,
          'school_1': 6, 'school_2': 5, 'school_3': 4,
          'college_1': 4, 'college_2': 3, 'college_3': 2
        };
        baseScore = practicalScores[level] || 0;
        // 团队分值调整：2-3人团队除以2，4人以上团队除以3
        if (teamSize >= 2 && teamSize <= 3) {
          baseScore = Math.round(baseScore / 2 * 10) / 10;
        } else if (teamSize >= 4) {
          baseScore = Math.round(baseScore / 3 * 10) / 10;
        }
        break;

      case '学生创新型项目':
        const innovationScores = {
          'national_1_leader': 10, 'national_2_leader': 9, 'national_3_leader': 8,
          'provincial_1_leader': 8, 'provincial_2_leader': 7, 'provincial_3_leader': 6,
          'school_1_leader': 6, 'school_2_leader': 5, 'school_3_leader': 4,
          'college_1_leader': 4, 'college_2_leader': 3, 'college_3_leader': 2,
          'national_1_member': 5, 'national_2_member': 4.5, 'national_3_member': 4,
          'provincial_1_member': 4, 'provincial_2_member': 3.5, 'provincial_3_member': 3,
          'school_1_member': 3, 'school_2_member': 2.5, 'school_3_member': 2,
          'college_1_member': 2, 'college_2_member': 1.5, 'college_3_member': 1
        };
        const innovationKey = `${level}_${role === 'leader' ? 'leader' : 'member'}`;
        baseScore = innovationScores[innovationKey] || 0;
        break;

      case '体育竞赛':
        const sportsScores = {
          'national_1': 10, 'national_2': 8, 'national_3': 7, 'national_other': 6,
          'provincial_1': 7, 'provincial_2': 6, 'provincial_3': 5, 'provincial_other': 4,
          'school_1': 5, 'school_2': 4, 'school_3': 3, 'school_other': 2,
          'college_1': 3, 'college_2': 2, 'college_3': 1, 'college_other': 0
        };
        baseScore = sportsScores[level] || 0;
        // 团队分值调整：2-3人团队除以2，4人以上团队除以3
        if (teamSize >= 2 && teamSize <= 3) {
          baseScore = Math.round(baseScore / 2 * 10) / 10;
        } else if (teamSize >= 4) {
          baseScore = Math.round(baseScore / 3 * 10) / 10;
        }
        break;

      case '文艺活动':
        const artsScores = {
          'national_1': 10, 'national_2': 8, 'national_3': 7,
          'provincial_1': 7, 'provincial_2': 6, 'provincial_3': 5,
          'school_1': 5, 'school_2': 4, 'school_3': 3,
          'college_1': 3, 'college_2': 2, 'college_3': 1
        };
        baseScore = artsScores[level] || 0;
        // 团队分值调整：2-3人团队除以2，4人以上团队除以3
        if (teamSize >= 2 && teamSize <= 3) {
          baseScore = Math.round(baseScore / 2 * 10) / 10;
        } else if (teamSize >= 4) {
          baseScore = Math.round(baseScore / 3 * 10) / 10;
        }
        break;

      case '英语六级':
        baseScore = 6;
        break;

      case '专业认证':
        const certScores = {
          'professional_high': 8,    // 高级认证
          'professional_mid': 6,     // 中级认证
          'professional_basic': 2,   // 初级认证
          'high_level': 8,           // 兼容旧格式
          'mid_level': 6,            // 兼容旧格式
          'basic_level': 2           // 兼容旧格式
        };
        baseScore = certScores[level] || 0;
        break;

      case '科学研究':
        const researchScores = {
          'core_journal': 6, 'patent_inventor': 15, 'patent_participant': 5
        };
        baseScore = researchScores[level] || 0;
        break;

      case '学生干部':
        const leadershipScores = {
          // 10分：校级以上学生组织主席团成员
          'school_president': 10,

          // 8分：院学生会主席团成员、青协主席团成员、年级长（团支书）、班长（团支书）
          'college_president': 8,
          'grade_leader': 8,
          'class_leader': 8,

          // 6分：年级委员、班委成员、各社团主席团成员、校院学生会部长、各社团部长、院资助站队长（副队长）
          'department_head': 6,
          'society_president': 6,
          'student_union_minister': 6,
          'society_minister': 6,
          'funding_captain': 6,

          // 3分：寝室长、教学联络员
          'dormitory_head': 3,
          'teaching_liaison': 3
        };
        baseScore = leadershipScores[level] || 0;
        break;

      case '社会实践志愿服务':
        const volunteerScores = {
          'national_individual': 10, 'provincial_individual': 8,
          'school_individual': 5, 'college_individual': 3,
          'national_key_team': 8, 'provincial_key_team': 6,
          'school_key_team': 4, 'college_key_team': 2,
          'national_normal_team': 4, 'provincial_normal_team': 3,
          'school_normal_team': 2, 'college_normal_team': 1
        };
        baseScore = volunteerScores[level] || 0;
        break;

      case '新闻采编写':
        baseScore = 0.1;
        break;

      case '见义勇为等表彰':
        const honorScores = {
          'national_honor': 5,    // 国家级表彰
          'provincial_honor': 4,  // 省级表彰
          'school_honor': 3,      // 校级表彰
          'national': 5,          // 兼容旧格式
          'provincial': 4,        // 兼容旧格式
          'city': 3,              // 兼容旧格式
          'school': 3             // 兼容旧格式
        };
        baseScore = honorScores[level] || 0;
        break;

      default:
        baseScore = 0;
    }

    return Math.round(baseScore * 10) / 10;
  };

  // 获取下一个待审核证书
  const getNextPendingCertificate = () => {
    const pendingCertificates = certificates.filter(cert => cert.status === 'pending');
    const currentPendingIndex = pendingCertificates.findIndex(cert => cert.id === currentCertificate.id);

    if (currentPendingIndex >= 0 && currentPendingIndex < pendingCertificates.length - 1) {
      return pendingCertificates[currentPendingIndex + 1];
    }
    return null;
  };

  // 获取上一个待审核证书
  const getPreviousPendingCertificate = () => {
    const pendingCertificates = certificates.filter(cert => cert.status === 'pending');
    const currentPendingIndex = pendingCertificates.findIndex(cert => cert.id === currentCertificate.id);

    if (currentPendingIndex > 0) {
      return pendingCertificates[currentPendingIndex - 1];
    }
    return null;
  };

  // 处理编辑证书信息
  const handleEditCertificate = async (values) => {
    try {
      await certificateAPI.updateCertificate(currentCertificate.id, values);
      message.success('证书信息更新成功');
      setEditModalVisible(false);
      editForm.resetFields();
      fetchCertificates();

      // 更新当前证书信息
      const updatedCertificate = { ...currentCertificate, ...values };
      setCurrentCertificate(updatedCertificate);
    } catch (error) {
      console.error('更新证书信息失败:', error);
      message.error('更新证书信息失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 删除证书
  const handleDeleteCertificate = async (certificateId) => {
    try {
      await certificateAPI.deleteCertificate(certificateId);
      message.success('证书删除成功');

      // 关闭审核模态框
      setAuditModalVisible(false);

      // 重新获取证书列表
      fetchCertificates();

      // 如果删除的是当前正在审核的证书，清空当前证书
      if (currentCertificate && currentCertificate.id === certificateId) {
        setCurrentCertificate(null);
      }
    } catch (error) {
      console.error('删除证书失败:', error);
      message.error('删除证书失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 批量删除证书
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的证书');
      return;
    }

    try {
      await certificateAPI.batchDeleteCertificates({ certificate_ids: selectedRowKeys });
      message.success(`成功删除 ${selectedRowKeys.length} 个证书`);

      // 重新获取证书列表
      fetchCertificates();

      // 清空选择
      setSelectedRowKeys([]);

      // 如果删除的证书包含当前正在审核的证书，关闭审核模态框
      if (currentCertificate && selectedRowKeys.includes(currentCertificate.id)) {
        setAuditModalVisible(false);
        setCurrentCertificate(null);
      }
    } catch (error) {
      console.error('批量删除证书失败:', error);
      message.error('批量删除证书失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 开始审核（打开审核模态框并设置当前索引）
  const startAudit = (record) => {
    setCurrentCertificate(record);
    setCurrentIndex(certificates.findIndex(cert => cert.id === record.id));
    setEditMode(false); // 重置编辑模式

    // 设置审核表单
    auditForm.setFieldsValue({
      action: 'approve',
      score: getDefaultScore(record.category_id, record.level, record.team_size, record.role),
      comment: ''
    });

    // 设置编辑表单
    editForm.setFieldsValue({
      category_id: record.category_id,
      certificate_name: record.certificate_name,
      level: record.level,
      award_date: record.award_date,
      team_size: record.team_size || 1,
      role: record.role || 'individual'
    });

    setAuditModalVisible(true);
  };

  const getStatusTag = (status) => {
    const statusMap = {
      pending: { color: 'orange', text: '待审核' },
      approved: { color: 'green', text: '已通过' },
      rejected: { color: 'red', text: '已拒绝' }
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getLevelText = (level) => {
    const levelMap = {
      // 通用等级
      'national_1': '国家级一等奖',
      'national_2': '国家级二等奖',
      'national_3': '国家级三等奖',
      'provincial_1': '省级一等奖',
      'provincial_2': '省级二等奖',
      'provincial_3': '省级三等奖',
      'school_1': '校级一等奖',
      'school_2': '校级二等奖',
      'school_3': '校级三等奖',
      'college_1': '院级一等奖',
      'college_2': '院级二等奖',
      'college_3': '院级三等奖',

      // 体育竞赛
      'national_other': '国家级其他名次（4-8名）',
      'provincial_other': '省级其他名次（4-8名）',
      'school_other': '校级其他名次（4-8名）',
      'college_other': '院级其他名次（不加分）',

      // 学生干部
      'school_president': '校级以上学生组织主席团成员',
      'college_president': '院学生会主席团成员/青协主席团成员',
      'grade_leader': '年级长（团支书）',
      'class_leader': '班长（团支书）',
      'department_head': '年级委员/班委成员',
      'society_president': '各社团主席团成员',
      'student_union_minister': '校院学生会部长',
      'society_minister': '各社团部长',
      'funding_captain': '院资助站队长（副队长）',
      'dormitory_head': '寝室长',
      'teaching_liaison': '教学联络员',

      // 社会实践志愿服务
      'national_individual': '国家级先进个人/优秀志愿者',
      'provincial_individual': '省级先进个人/优秀志愿者',
      'school_individual': '校级先进个人/优秀志愿者',
      'college_individual': '院级先进个人/优秀志愿者',
      'national_key_team': '国家级重点团队成员',
      'provincial_key_team': '省级重点团队成员',
      'school_key_team': '校级重点团队成员',
      'college_key_team': '院级重点团队成员',
      'national_normal_team': '国家级非重点团队成员',
      'provincial_normal_team': '省级非重点团队成员',
      'school_normal_team': '校级非重点团队成员',
      'college_normal_team': '院级非重点团队成员',

      // 专业认证
      'professional_high': '高级认证',
      'professional_mid': '中级认证',
      'professional_basic': '初级认证',
      'professional_low': '初级认证',

      // 科学研究
      'core_journal': '核心期刊论文',
      'patent_inventor': '发明专利发明人',
      'patent_participant': '发明专利参与者',

      // 英语六级
      'english_cet6': '通过六级考试',
      'cet6_excellent': '六级优秀(≥550分)',
      'cet6_good': '六级良好(500-549分)',
      'cet6_pass': '六级通过(425-499分)',

      // 见义勇为等表彰
      'national_honor': '国家级表彰',
      'provincial_honor': '省级表彰',
      'school_honor': '校级表彰',
      'national': '国家级表彰',
      'provincial': '省级表彰',
      'city': '市级表彰',
      'school': '校级表彰',

      // 新闻采编写
      'news_activity': '新闻采编写活动',

      'other': '其他'
    };
    return levelMap[level] || level;
  };

  const handleSearch = (value) => {
    setSearchText(value);
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchCertificates({ search: value, page: 1 });
  };

  const handleStatusFilter = (value) => {
    setStatusFilter(value);
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchCertificates({ status: value, page: 1 });
  };

  const handleCategoryFilter = (value) => {
    setCategoryFilter(value);
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchCertificates({ category_id: value, page: 1 });
  };

  const handleTableChange = (paginationInfo) => {
    setPagination(paginationInfo);
    fetchCertificates({
      page: paginationInfo.current,
      limit: paginationInfo.pageSize
    });
  };

  const columns = [
    {
      title: '学生信息',
      key: 'student_info',
      width: 150,
      render: (_, record) => (
        <div>
          <Text strong>{record.student_name}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.student_id}
          </Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.class_name}
          </Text>
        </div>
      ),
    },
    {
      title: '证书信息',
      key: 'certificate_info',
      render: (_, record) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Text strong ellipsis>{record.certificate_name}</Text>
            {record.is_custom_activity && (
              <Tag color="orange" size="small">自定义</Tag>
            )}
          </div>
          <br />
          <Text type="secondary">{record.category_name}</Text>
          <br />
          <Tag size="small">{getLevelText(record.level)}</Tag>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => getStatusTag(status),
    },
    {
      title: '得分',
      dataIndex: 'score',
      key: 'score',
      width: 80,
      render: (score) => score ? `${score}分` : '-',
    },
    {
      title: '提交时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (time) => {
        if (!time) return '-';
        try {
          return new Date(time).toLocaleString('zh-CN');
        } catch (error) {
          return '-';
        }
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
      render: (_, record) => {
        const isPending = record.status === 'pending';
        const isApproved = record.status === 'approved';
        const isRejected = record.status === 'rejected';

        return (
          <Space direction="vertical" size={4} style={{ width: '100%' }}>
            {/* 第一行：基础操作 */}
            <Space size="small">
              <Button
                type="link"
                size="small"
                icon={<EyeOutlined />}
                onClick={() => {
                  setCurrentCertificate(record);
                  setViewModalVisible(true);
                }}
              >
                查看
              </Button>

              <Button
                type="link"
                size="small"
                onClick={() => {
                  setCurrentCertificate(record);
                  editForm.setFieldsValue({
                    certificate_name: record.certificate_name,
                    level: record.level,
                    award_date: record.award_date,
                    team_size: record.team_size,
                    role: record.role
                  });
                  setEditModalVisible(true);
                }}
              >
                编辑
              </Button>
            </Space>

            {/* 第二行：审核操作 */}
            {isPending && (
              <Space size="small">
                <Button
                  type="primary"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => startAudit(record)}
                >
                  审核
                </Button>
                <Button
                  size="small"
                  style={{ color: '#52c41a', borderColor: '#52c41a' }}
                  onClick={() => handleQuickApprove(record)}
                >
                  通过
                </Button>
                <Button
                  size="small"
                  danger
                  onClick={() => handleQuickReject(record)}
                >
                  拒绝
                </Button>
              </Space>
            )}

            {/* 已审核状态：重新审核 */}
            {(isApproved || isRejected) && (
              <Button
                type="default"
                size="small"
                icon={<EditOutlined />}
                onClick={() => startAudit(record)}
                style={{ width: 'fit-content' }}
              >
                重新审核
              </Button>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={2}>
            <CheckCircleOutlined /> 证书审核
          </Title>
        </Col>
      </Row>

      {/* 审核活动选择提示 */}
      {auditActivities.length === 0 ? (
        <Card style={{ marginBottom: 16 }}>
          <div style={{ textAlign: 'center', padding: '40px 20px' }}>
            <FileTextOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: 16 }} />
            <div style={{ fontSize: '16px', color: '#666', marginBottom: 8 }}>
              暂无审核活动
            </div>
            <div style={{ fontSize: '14px', color: '#999', marginBottom: 16 }}>
              请先创建审核活动，然后学生才能提交证书进行审核
            </div>
            <Button
              type="primary"
              onClick={() => window.location.href = '/teacher/audit-activities'}
            >
              去创建审核活动
            </Button>
          </div>
        </Card>
      ) : (
        <>
          {/* 搜索和筛选 */}
          <Card style={{ marginBottom: 16 }}>
            <Row gutter={[16, 16]} align="middle">
              <Col xs={24} sm={12} md={6}>
                <Select
                  placeholder="选择审核活动"
                  allowClear
                  style={{ width: '100%' }}
                  onChange={(value) => {
                    setSelectedActivity(value);
                    setPagination(prev => ({ ...prev, current: 1 }));
                    fetchCertificates({ audit_activity_id: value });
                  }}
                  value={selectedActivity || undefined}
                  notFoundContent={
                    <div style={{ textAlign: 'center', padding: '20px' }}>
                      <div style={{ color: '#999', marginBottom: 8 }}>暂无审核活动</div>
                      <Button
                        type="link"
                        size="small"
                        onClick={() => window.location.href = '/teacher/audit-activities'}
                      >
                        去创建
                      </Button>
                    </div>
                  }
                >
                  {auditActivities.map(activity => (
                    <Option key={activity.id} value={activity.id}>
                      {activity.name}
                    </Option>
                  ))}
                </Select>
              </Col>
          <Col xs={24} sm={12} md={6}>
            <Search
              placeholder="搜索学生姓名、证书名称"
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Select
              placeholder="选择状态"
              allowClear
              style={{ width: '100%' }}
              onChange={handleStatusFilter}
              value={statusFilter || undefined}
            >
              <Option value="pending">待审核</Option>
              <Option value="approved">已通过</Option>
              <Option value="rejected">已拒绝</Option>
            </Select>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Select
              placeholder="选择类别"
              allowClear
              style={{ width: '100%' }}
              onChange={handleCategoryFilter}
              value={categoryFilter || undefined}
            >
              {categories.filter(cat => cat.parent_id).map(category => (
                <Option key={category.id} value={category.id}>
                  {category.category_name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Button 
              icon={<ReloadOutlined />}
              onClick={() => {
                setSearchText('');
                setStatusFilter('');
                setCategoryFilter('');
                fetchCertificates({ search: '', status: '', category_id: '', page: 1 });
              }}
            >
              重置
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 证书列表 */}
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>证书列表</span>
            {selectedRowKeys.length > 0 && (
              <Space>
                <span style={{ color: '#666' }}>
                  已选择 {selectedRowKeys.length} 项
                </span>
                <Popconfirm
                  title="批量删除证书"
                  description={`确定要删除选中的 ${selectedRowKeys.length} 个证书吗？删除后无法恢复。`}
                  onConfirm={handleBatchDelete}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button danger icon={<DeleteOutlined />}>
                    批量删除
                  </Button>
                </Popconfirm>
              </Space>
            )}
          </div>
        }
      >
        <Table
          columns={columns}
          dataSource={certificates}
          rowKey="id"
          loading={loading}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
            getCheckboxProps: (record) => ({
              name: record.certificate_name,
            }),
          }}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 查看证书模态框 */}
      <Modal
        title="证书详情"
        open={viewModalVisible}
        onCancel={() => {
          setViewModalVisible(false);
          setCurrentCertificate(null);
        }}
        footer={null}
        width={600}
      >
        {currentCertificate && (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Text strong>学生姓名：</Text>
                <Text>{currentCertificate.student_name}</Text>
              </Col>
              <Col span={12}>
                <Text strong>学号：</Text>
                <Text>{currentCertificate.student_id}</Text>
              </Col>
              <Col span={12}>
                <Text strong>班级：</Text>
                <Text>{currentCertificate.class_name}</Text>
              </Col>
              <Col span={12}>
                <Text strong>证书类别：</Text>
                <Text>{currentCertificate.category_name}</Text>
              </Col>
              <Col span={24}>
                <Text strong>证书名称：</Text>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Text>{currentCertificate.certificate_name}</Text>
                  {currentCertificate.is_custom_activity && (
                    <Tag color="orange" size="small">自定义活动</Tag>
                  )}
                </div>
              </Col>
              <Col span={12}>
                <Text strong>获奖等级：</Text>
                <Text>{getLevelText(currentCertificate.level)}</Text>
              </Col>
              <Col span={12}>
                <Text strong>提交时间：</Text>
                <Text>{currentCertificate.created_at ? new Date(currentCertificate.created_at).toLocaleString('zh-CN') : '-'}</Text>
              </Col>
              <Col span={12}>
                <Text strong>审核状态：</Text>
                {getStatusTag(currentCertificate.status)}
              </Col>
              <Col span={12}>
                <Text strong>得分：</Text>
                <Text>{currentCertificate.score ? `${currentCertificate.score}分` : '-'}</Text>
              </Col>
            </Row>
            
            {currentCertificate.file_path && (
              <div style={{ marginTop: 16 }}>
                <Text strong>证书文件：</Text>
                <div style={{ marginTop: 8 }}>
                  <Image
                    src={`/api/uploads/${currentCertificate.file_path}`}
                    alt="证书文件"
                    style={{ maxWidth: '100%' }}
                    onError={(e) => {
                      console.error('图片加载失败:', e);
                    }}
                  />
                </div>
              </div>
            )}
            
            {currentCertificate.audit_comment && (
              <div style={{ marginTop: 16 }}>
                <Text strong>审核意见：</Text>
                <div style={{ marginTop: 8, padding: 8, background: '#f5f5f5', borderRadius: 4 }}>
                  <Text>{currentCertificate.audit_comment}</Text>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 审核模态框 */}
      <Modal
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>审核证书</span>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {(() => {
                const pendingCertificates = certificates.filter(cert => cert.status === 'pending');
                const currentPendingIndex = pendingCertificates.findIndex(cert => cert.id === currentCertificate?.id);
                return `${currentPendingIndex + 1} / ${pendingCertificates.length}`;
              })()}
            </div>
          </div>
        }
        open={auditModalVisible}
        onCancel={() => {
          setAuditModalVisible(false);
          auditForm.resetFields();
          editForm.resetFields();
          setCurrentCertificate(null);
          setEditMode(false);
        }}
        footer={null}
        width={800}
      >
        {currentCertificate && (
          <div>
            {/* 证书信息展示 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={12}>
                <div style={{ padding: 12, background: '#f5f5f5', borderRadius: 4, height: '100%' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                    <Title level={5} style={{ margin: 0 }}>证书信息</Title>
                    <Button
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => setEditMode(!editMode)}
                      type={editMode ? 'primary' : 'default'}
                    >
                      {editMode ? '取消编辑' : '编辑信息'}
                    </Button>
                  </div>
                  {!editMode ? (
                    // 信息展示模式
                    <>
                      <div style={{ marginBottom: 8 }}>
                        <Text strong>学生：</Text>
                        <Text>{currentCertificate.student_name} ({currentCertificate.student_id})</Text>
                      </div>
                      <div style={{ marginBottom: 8 }}>
                        <Text strong>班级：</Text>
                        <Text>{currentCertificate.class_name}</Text>
                      </div>
                      <div style={{ marginBottom: 8 }}>
                        <Text strong>证书类别：</Text>
                        <Text>{currentCertificate.category_name}</Text>
                      </div>
                      <div style={{ marginBottom: 8 }}>
                        <Text strong>证书名称：</Text>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                          <Text>{currentCertificate.certificate_name}</Text>
                          {currentCertificate.is_custom_activity && (
                            <Tag color="orange" size="small">自定义活动</Tag>
                          )}
                        </div>
                      </div>
                      <div style={{ marginBottom: 8 }}>
                        <Text strong>获奖等级：</Text>
                        <Text>{getLevelText(currentCertificate.level)}</Text>
                      </div>
                      <div style={{ marginBottom: 8 }}>
                        <Text strong>获奖日期：</Text>
                        <Text>{currentCertificate.award_date || '-'}</Text>
                      </div>
                      {currentCertificate.team_size > 1 && (
                        <div style={{ marginBottom: 8 }}>
                          <Text strong>团队信息：</Text>
                          <Text>{currentCertificate.team_size}人团队 - {currentCertificate.role === 'leader' ? '队长' : '队员'}</Text>
                        </div>
                      )}
                      <div style={{ marginBottom: 8 }}>
                        <Text strong>提交时间：</Text>
                        <Text>{currentCertificate.created_at ? new Date(currentCertificate.created_at).toLocaleString('zh-CN') : '-'}</Text>
                      </div>
                    </>
                  ) : (
                    // 编辑模式
                    <Form
                      form={editForm}
                      layout="vertical"
                      onFinish={handleCertificateEdit}
                      size="small"
                    >
                      <Form.Item
                        name="category_id"
                        label="证书类别"
                        rules={[{ required: true, message: '请选择证书类别' }]}
                      >
                        <Select placeholder="请选择证书类别">
                          {categories.map(category => (
                            <Option key={category.id} value={category.id}>
                              {category.category_name}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>

                      <Form.Item
                        name="certificate_name"
                        label="证书名称"
                      >
                        <Input placeholder="请输入证书名称" />
                      </Form.Item>

                      <Form.Item
                        name="level"
                        label="获奖等级"
                        rules={[{ required: true, message: '请选择获奖等级' }]}
                      >
                        <Select placeholder="请选择获奖等级">
                          {/* 这里需要根据选择的类别动态生成选项 */}
                          <Option value="national_1">国家级一等奖</Option>
                          <Option value="national_2">国家级二等奖</Option>
                          <Option value="national_3">国家级三等奖</Option>
                          <Option value="provincial_1">省级一等奖</Option>
                          <Option value="provincial_2">省级二等奖</Option>
                          <Option value="provincial_3">省级三等奖</Option>
                          <Option value="school_1">校级一等奖</Option>
                          <Option value="school_2">校级二等奖</Option>
                          <Option value="school_3">校级三等奖</Option>
                          <Option value="college_1">院级一等奖</Option>
                          <Option value="college_2">院级二等奖</Option>
                          <Option value="college_3">院级三等奖</Option>
                        </Select>
                      </Form.Item>

                      <Form.Item
                        name="award_date"
                        label="获奖日期"
                      >
                        <Input type="date" />
                      </Form.Item>

                      <Form.Item
                        name="team_size"
                        label="团队人数"
                      >
                        <InputNumber min={1} max={20} />
                      </Form.Item>

                      <Form.Item
                        name="role"
                        label="角色"
                      >
                        <Select>
                          <Option value="individual">个人</Option>
                          <Option value="leader">队长</Option>
                          <Option value="member">队员</Option>
                        </Select>
                      </Form.Item>

                      <Form.Item>
                        <Space>
                          <Button type="primary" htmlType="submit" size="small">
                            保存修改
                          </Button>
                          <Button onClick={() => setEditMode(false)} size="small">
                            取消
                          </Button>
                        </Space>
                      </Form.Item>
                    </Form>
                  )}
                </div>
              </Col>
              <Col span={12}>
                <div style={{ padding: 12, background: '#f5f5f5', borderRadius: 4, height: '100%' }}>
                  <Title level={5}>证书文件</Title>
                  {currentCertificate.file_path ? (
                    <Image
                      src={`/api/uploads/${currentCertificate.file_path}`}
                      alt="证书文件"
                      style={{ width: '100%', maxHeight: '300px', objectFit: 'contain' }}
                      onError={(e) => {
                        console.error('图片加载失败:', e);
                      }}
                    />
                  ) : (
                    <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                      暂无证书文件
                    </div>
                  )}
                </div>
              </Col>
            </Row>

            <Form
              form={auditForm}
              layout="vertical"
              onFinish={handleAudit}
            >

            <Form.Item
              name="action"
              label="审核结果"
              rules={[{ required: true, message: '请选择审核结果' }]}
            >
              <Radio.Group>
                <Radio value="approve">通过</Radio>
                <Radio value="reject">拒绝</Radio>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              name="score"
              label="得分"
              rules={[{ required: true, message: '请输入得分' }]}
            >
              <InputNumber
                min={0}
                max={30}
                precision={1}
                style={{ width: '100%' }}
                placeholder="请输入得分"
              />
            </Form.Item>

            <Form.Item
              name="comment"
              label="审核意见"
            >
              <TextArea
                rows={3}
                placeholder="请输入审核意见（可选）"
              />
            </Form.Item>

            {/* 自动跳转控制 */}
            <Form.Item>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <input
                  type="checkbox"
                  id="autoNext"
                  checked={autoNext}
                  onChange={(e) => setAutoNext(e.target.checked)}
                />
                <label htmlFor="autoNext">审核完成后自动跳转到下一个</label>
              </div>
            </Form.Item>

            <Divider />

            {/* 导航和操作按钮 */}
            <Form.Item style={{ marginBottom: 0 }}>
              <Row justify="space-between" align="middle">
                <Col>
                  <Space>
                    <Button
                      disabled={!getPreviousPendingCertificate()}
                      onClick={() => {
                        const prevCert = getPreviousPendingCertificate();
                        if (prevCert) {
                          setCurrentCertificate(prevCert);
                          form.setFieldsValue({
                            action: 'approve',
                            score: getDefaultScore(prevCert.category_id, prevCert.level, prevCert.team_size, prevCert.role),
                            comment: ''
                          });
                        }
                      }}
                    >
                      上一个
                    </Button>
                    <Button
                      disabled={!getNextPendingCertificate()}
                      onClick={() => {
                        const nextCert = getNextPendingCertificate();
                        if (nextCert) {
                          setCurrentCertificate(nextCert);
                          form.setFieldsValue({
                            action: 'approve',
                            score: getDefaultScore(nextCert.category_id, nextCert.level, nextCert.team_size, nextCert.role),
                            comment: ''
                          });
                        }
                      }}
                    >
                      下一个
                    </Button>
                  </Space>
                </Col>
                <Col>
                  <Space>
                    <Button onClick={() => {
                      setCurrentCertificate(currentCertificate);
                      editForm.setFieldsValue({
                        certificate_name: currentCertificate.certificate_name,
                        level: currentCertificate.level,
                        award_date: currentCertificate.award_date,
                        team_size: currentCertificate.team_size,
                        role: currentCertificate.role
                      });
                      setEditModalVisible(true);
                    }}>
                      编辑信息
                    </Button>
                    <Popconfirm
                      title="删除证书"
                      description="确定要删除这个证书吗？删除后无法恢复。"
                      onConfirm={() => handleDeleteCertificate(currentCertificate.id)}
                      okText="确定"
                      cancelText="取消"
                    >
                      <Button danger icon={<DeleteOutlined />}>
                        删除证书
                      </Button>
                    </Popconfirm>
                    <Button onClick={() => setAuditModalVisible(false)}>
                      取消
                    </Button>
                    <Button type="primary" htmlType="submit">
                      提交审核
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Form.Item>
          </Form>
          </div>
        )}
      </Modal>

      {/* 编辑证书信息模态框 */}
      <Modal
        title="编辑证书信息"
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        {currentCertificate && (
          <Form
            form={editForm}
            layout="vertical"
            onFinish={handleEditCertificate}
          >
            <div style={{ marginBottom: 16, padding: 12, background: '#f5f5f5', borderRadius: 4 }}>
              <Text strong>{currentCertificate.student_name}</Text> - {currentCertificate.category_name}
            </div>

            <Form.Item
              name="certificate_name"
              label="证书名称"
              rules={[{ required: true, message: '请输入证书名称' }]}
            >
              <Input placeholder="请输入证书名称" />
            </Form.Item>

            <Form.Item
              name="level"
              label="获奖等级"
              rules={[{ required: true, message: '请选择获奖等级' }]}
            >
              <Select placeholder="请选择获奖等级">
                {currentCertificate && (() => {
                  const category = categories.find(cat => cat.id === currentCertificate.category_id);
                  if (!category) return null;

                  return category.level_options?.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ));
                })()}
              </Select>
            </Form.Item>

            <Form.Item
              name="award_date"
              label="获奖日期"
            >
              <Input type="date" />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="team_size"
                  label="团队人数"
                >
                  <Select placeholder="请选择团队人数">
                    <Option value={1}>个人</Option>
                    <Option value={2}>2人团队</Option>
                    <Option value={3}>3人团队</Option>
                    <Option value={4}>4人及以上团队</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="role"
                  label="团队角色"
                >
                  <Select placeholder="请选择团队角色">
                    <Option value="individual">个人</Option>
                    <Option value="leader">队长</Option>
                    <Option value="member">队员</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Divider />

            <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
              <Space>
                <Button onClick={() => setEditModalVisible(false)}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit">
                  保存修改
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>
      </>
      )}
    </div>
  );
};

export default CertificateAudit;
