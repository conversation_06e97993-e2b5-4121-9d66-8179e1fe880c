import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Typography,
  Spin,
  Table,
  Tag,
  Alert,
  Descriptions,
  Divider,
  Select
} from 'antd';
import { 
  TrophyOutlined, 
  FileTextOutlined, 
  CheckCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { scoreAPI, certificateAPI, auditActivityAPI } from '../../utils/api';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const MyScores = () => {
  const [loading, setLoading] = useState(true);
  const [scoreData, setScoreData] = useState(null);
  const [certificates, setCertificates] = useState([]);
  const [auditActivities, setAuditActivities] = useState([]);
  const [selectedActivity, setSelectedActivity] = useState(null);

  useEffect(() => {
    fetchAuditActivities();
    // 先加载全部成绩，不依赖活动
    fetchData();
  }, []);

  useEffect(() => {
    if (selectedActivity !== null) {
      fetchData();
    }
  }, [selectedActivity]);

  const fetchAuditActivities = async () => {
    try {
      const response = await auditActivityAPI.getActivities();
      const activities = response.data?.activities || response.data || [];
      const activeActivities = activities.filter(activity =>
        activity.status === 'active' &&
        (!activity.end_date || new Date(activity.end_date + 'T23:59:59') >= new Date())
      );
      setAuditActivities(activeActivities);

      // 默认选择第一个审核活动
      if (activeActivities.length > 0 && selectedActivity === null) {
        setSelectedActivity(activeActivities[0].id);
      }
    } catch (error) {
      console.error('获取审核活动失败:', error);
      // 即使获取活动失败，也不影响成绩显示
    }
  };

  const fetchData = async () => {
    try {
      setLoading(true);

      // 获取成绩数据（如果有选择活动则基于活动，否则显示全部）
      const scoreParams = selectedActivity ? { audit_activity_id: selectedActivity } : {};
      const scoreResponse = await scoreAPI.getMyScore(scoreParams);
      setScoreData(scoreResponse.data.score);

      // 获取已通过的证书（如果有选择活动则基于活动，否则显示全部）
      const certParams = {
        status: 'approved',
        limit: 1000,
        ...(selectedActivity && { audit_activity_id: selectedActivity })
      };
      const certResponse = await certificateAPI.getMyCertificates(certParams);
      setCertificates(certResponse.data.certificates || []);

    } catch (error) {
      console.error('获取数据失败:', error);
      // 即使获取失败，也要停止loading
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  const score = scoreData || {
    base_score: 25,

    // 专业类详细分类
    academic_competition: 0,
    practical_skills: 0,
    innovation_project: 0,
    professional_cert: 0,
    research: 0,
    professional_total: 0,

    // 体育美育类详细分类
    sports_competition: 0,
    arts_activity: 0,
    english_cet6: 0,
    sports_arts_total: 0,

    // 文明品德类详细分类
    student_leader: 0,
    volunteer_service: 0,
    news_writing: 0,
    moral_honor: 0,
    moral_total: 0,

    // 总分和最终成绩
    total: 25,
    final: 5
  };

  const getLevelText = (level) => {
    const levelMap = {
      // 通用等级
      'national_1': '国家级一等奖',
      'national_2': '国家级二等奖',
      'national_3': '国家级三等奖',
      'provincial_1': '省级一等奖',
      'provincial_2': '省级二等奖',
      'provincial_3': '省级三等奖',
      'school_1': '校级一等奖',
      'school_2': '校级二等奖',
      'school_3': '校级三等奖',
      'college_1': '院级一等奖',
      'college_2': '院级二等奖',
      'college_3': '院级三等奖',

      // 体育竞赛
      'national_other': '国家级其他名次（4-8名）',
      'provincial_other': '省级其他名次（4-8名）',
      'school_other': '校级其他名次（4-8名）',
      'college_other': '院级其他名次（不加分）',

      // 学生干部
      'school_president': '校级以上学生组织主席团成员',
      'college_president': '院学生会主席团成员/青协主席团成员',
      'grade_leader': '年级长（团支书）',
      'class_leader': '班长（团支书）',
      'department_head': '年级委员/班委成员',
      'society_president': '各社团主席团成员',
      'student_union_minister': '校院学生会部长',
      'society_minister': '各社团部长',
      'funding_captain': '院资助站队长（副队长）',
      'dormitory_head': '寝室长',
      'teaching_liaison': '教学联络员',

      // 社会实践志愿服务
      'national_individual': '国家级先进个人/优秀志愿者',
      'provincial_individual': '省级先进个人/优秀志愿者',
      'school_individual': '校级先进个人/优秀志愿者',
      'college_individual': '院级先进个人/优秀志愿者',
      'national_key_team': '国家级重点团队成员',
      'provincial_key_team': '省级重点团队成员',
      'school_key_team': '校级重点团队成员',
      'college_key_team': '院级重点团队成员',
      'national_normal_team': '国家级非重点团队成员',
      'provincial_normal_team': '省级非重点团队成员',
      'school_normal_team': '校级非重点团队成员',
      'college_normal_team': '院级非重点团队成员',

      // 专业认证
      'professional_high': '高级认证',
      'professional_mid': '中级认证',
      'professional_basic': '初级认证',
      'professional_low': '初级认证',

      // 科学研究
      'core_journal': '核心期刊论文',
      'patent_inventor': '发明专利发明人',
      'patent_participant': '发明专利参与者',

      // 英语六级
      'english_cet6': '通过六级考试',
      'cet6_excellent': '六级优秀(≥550分)',
      'cet6_good': '六级良好(500-549分)',
      'cet6_pass': '六级通过(425-499分)',

      // 见义勇为等表彰
      'national_honor': '国家级表彰',
      'provincial_honor': '省级表彰',
      'school_honor': '校级表彰',
      'national': '国家级表彰',
      'provincial': '省级表彰',
      'city': '市级表彰',
      'school': '校级表彰',

      // 新闻采编写
      'news_activity': '新闻采编写活动',

      'other': '其他'
    };
    return levelMap[level] || level;
  };

  const certificateColumns = [
    {
      title: '证书名称',
      dataIndex: 'certificate_name',
      key: 'certificate_name',
      ellipsis: true,
    },
    {
      title: '类别',
      dataIndex: 'category_name',
      key: 'category_name',
    },
    {
      title: '等级',
      dataIndex: 'level',
      key: 'level',
      render: (level) => getLevelText(level),
    },
    {
      title: '得分',
      dataIndex: 'score',
      key: 'score',
      render: (score) => (
        <Tag color="green">
          <TrophyOutlined /> {score}分
        </Tag>
      ),
    },
    {
      title: '审核时间',
      dataIndex: 'audit_time',
      key: 'audit_time',
      render: (time) => time ? new Date(time).toLocaleDateString() : '-',
    },
  ];

  return (
    <div>
      <Title level={2}>
        <TrophyOutlined /> 我的成绩
      </Title>

      {/* 审核活动选择 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Text strong>选择审核活动：</Text>
          </Col>
          <Col span={12}>
            <Select
              style={{ width: '100%' }}
              placeholder="请选择审核活动"
              value={selectedActivity}
              onChange={setSelectedActivity}
              allowClear
            >
              <Option key="all" value={null}>
                全部成绩
              </Option>
              {auditActivities.map(activity => (
                <Option key={activity.id} value={activity.id}>
                  {activity.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            {selectedActivity && (
              <Alert
                message="当前查看活动"
                description={auditActivities.find(a => a.id === selectedActivity)?.name}
                type="info"
                showIcon
                size="small"
              />
            )}
          </Col>
        </Row>
      </Card>

      {!selectedActivity ? (
        <Alert
          message="请选择审核活动"
          description="选择审核活动后查看对应的成绩信息"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      ) : (
        <>
          {/* 成绩总览 */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="最终成绩"
              value={score.final}
              precision={2}
              suffix="分"
              prefix={<TrophyOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14', fontSize: '28px' }}
            />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              总分 × 20%
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总分"
              value={score.total}
              precision={2}
              suffix="分"
              valueStyle={{ color: '#1890ff', fontSize: '24px' }}
            />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              各项分值总和
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已获得证书"
              value={certificates.length}
              suffix="个"
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a', fontSize: '24px' }}
            />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              已通过审核
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总得分"
              value={certificates.reduce((sum, cert) => sum + (cert.score || 0), 0)}
              precision={2}
              suffix="分"
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#722ed1', fontSize: '24px' }}
            />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              证书累计得分
            </Text>
          </Card>
        </Col>
      </Row>

      {/* 成绩详细分解 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={16}>
          <Card title="成绩详细分解" extra={<InfoCircleOutlined />}>
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12}>
                <div style={{ marginBottom: 20 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                    <Text strong>基础分值</Text>
                    <Text strong style={{ color: '#52c41a' }}>
                      {score.base_score}/25分
                    </Text>
                  </div>
                  <Progress
                    percent={(score.base_score / 25) * 100}
                    strokeColor="#52c41a"
                    showInfo={false}
                  />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    每人基础分值25分，本学年无违纪现象
                  </Text>
                </div>

                <div style={{ marginBottom: 20 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                    <Text strong>专业类活动分值</Text>
                    <Text strong style={{ color: '#1890ff' }}>
                      {score.professional_total}/30分
                    </Text>
                  </div>
                  <Progress
                    percent={(score.professional_total / 30) * 100}
                    strokeColor="#1890ff"
                    showInfo={false}
                  />
                  <div style={{ marginTop: 8, fontSize: '12px' }}>
                    <div>学科竞赛：{score.academic_competition}分</div>
                    <div>实践技能：{score.practical_skills}分</div>
                    <div>创新项目：{score.innovation_project}分</div>
                    <div>专业认证：{score.professional_cert}分</div>
                    <div>科学研究：{score.research}分</div>
                  </div>
                </div>

                <div style={{ marginBottom: 20 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                    <Text strong>体育美育类分值</Text>
                    <Text strong style={{ color: '#722ed1' }}>
                      {score.sports_arts_total}/20分
                    </Text>
                  </div>
                  <Progress
                    percent={(score.sports_arts_total / 20) * 100}
                    strokeColor="#722ed1"
                    showInfo={false}
                  />
                  <div style={{ marginTop: 8, fontSize: '12px' }}>
                    <div>体育竞赛：{score.sports_competition}分</div>
                    <div>文艺活动：{score.arts_activity}分</div>
                    <div>英语六级：{score.english_cet6}分</div>
                  </div>
                </div>

                <div style={{ marginBottom: 20 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                    <Text strong>文明品德类分值</Text>
                    <Text strong style={{ color: '#fa8c16' }}>
                      {score.moral_total}/25分
                    </Text>
                  </div>
                  <Progress
                    percent={(score.moral_total / 25) * 100}
                    strokeColor="#fa8c16"
                    showInfo={false}
                  />
                  <div style={{ marginTop: 8, fontSize: '12px' }}>
                    <div>学生干部：{score.student_leader}分</div>
                    <div>志愿服务：{score.volunteer_service}分</div>
                    <div>新闻采编：{score.news_writing}分</div>
                    <div>道德表彰：{score.moral_honor}分</div>
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={12}>
                <div style={{ marginBottom: 20 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                    <Text strong>分数构成</Text>
                  </div>
                  <div style={{ fontSize: '14px', lineHeight: '1.8' }}>
                    <div>基础分值：{score.base_score}分</div>
                    <div>专业类活动：{score.professional_total}分</div>
                    <div>体育美育类：{score.sports_arts_total}分</div>
                    <div>文明品德类：{score.moral_total}分</div>
                    <Divider style={{ margin: '8px 0' }} />
                    <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
                      总分：{score.total}分
                    </div>
                    <div style={{ fontWeight: 'bold', color: '#faad14' }}>
                      最终成绩：{score.final}分 (总分×20%)
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="计算规则说明">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="基础分值">25分（固定）</Descriptions.Item>
              <Descriptions.Item label="专业类活动">最高30分</Descriptions.Item>
              <Descriptions.Item label="体育美育">最高20分</Descriptions.Item>
              <Descriptions.Item label="文明品德">最高25分</Descriptions.Item>
            </Descriptions>
            
            <Divider />
            
            <Alert
              message="计算公式"
              description={
                <div>
                  <Paragraph style={{ margin: 0 }}>
                    <Text strong>最终成绩 = 总分 × 20%</Text>
                  </Paragraph>
                  <Paragraph style={{ margin: 0, marginTop: 8 }}>
                    <Text type="secondary">
                      总分 = 基础分值 + 专业类活动分值 + 体育美育分值 + 文明品德分值
                    </Text>
                  </Paragraph>
                </div>
              }
              type="info"
              showIcon
            />
          </Card>
        </Col>
      </Row>

      {/* 已获得证书列表 */}
      <Card title="已获得证书" extra={`共 ${certificates.length} 个`}>
        {certificates.length > 0 ? (
          <Table
            columns={certificateColumns}
            dataSource={certificates}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 条记录`,
            }}
          />
        ) : (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <FileTextOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
            <div style={{ marginTop: 16 }}>
              <Text type="secondary">暂无已通过审核的证书</Text>
            </div>
          </div>
        )}
      </Card>
        </>
      )}
    </div>
  );
};

export default MyScores;
