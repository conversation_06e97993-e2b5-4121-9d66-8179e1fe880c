import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  message, 
  Space,
  Typography,
  Row,
  Col,
  Tag,
  DatePicker,
  Divider,
  Popconfirm
} from 'antd';
import { 
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CalendarOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { auditActivityAPI, categoryAPI, adminAPI } from '../../utils/api';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

const AuditActivityManagement = () => {
  const [loading, setLoading] = useState(false);
  const [activities, setActivities] = useState([]);
  const [categories, setCategories] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingActivity, setEditingActivity] = useState(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchActivities();
    fetchCategories();
  }, []);

  const fetchActivities = async () => {
    try {
      setLoading(true);
      const response = await auditActivityAPI.getActivities();
      setActivities(response.data.activities || response.data || []);
    } catch (error) {
      console.error('获取审核活动列表失败:', error);
      message.error('获取审核活动列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await categoryAPI.getCategories();
      setCategories(response.data.flat_categories || []);
    } catch (error) {
      console.error('获取类别列表失败:', error);
    }
  };

  const handleSubmit = async (values) => {
    try {
      const formData = {
        title: values.title,
        description: values.description,
        start_date: values.dateRange[0].format('YYYY-MM-DD'),
        end_date: values.dateRange[1].format('YYYY-MM-DD'),
        categories: values.categories || [],
        status: values.status || 'active'
      };

      if (editingActivity) {
        await auditActivityAPI.updateActivity(editingActivity.id, formData);
        message.success('审核活动更新成功');
      } else {
        await auditActivityAPI.createActivity(formData);
        message.success('审核活动创建成功');
      }

      setModalVisible(false);
      form.resetFields();
      setEditingActivity(null);
      fetchActivities();
    } catch (error) {
      console.error('操作失败:', error);
      message.error('操作失败: ' + (error.response?.data?.message || error.message));
    }
  };

  const handleDelete = async (id) => {
    try {
      const response = await auditActivityAPI.deleteActivity(id);
      const { deletedCertificatesCount, deletedCertificates } = response.data;

      if (deletedCertificatesCount > 0) {
        message.success(`审核活动删除成功，同时删除了 ${deletedCertificatesCount} 个相关证书`);
        console.log('删除的证书详情:', deletedCertificates);
      } else {
        message.success('审核活动删除成功，该活动下没有关联的证书');
      }

      fetchActivities();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 清理无效证书数据
  const handleCleanupCertificates = async () => {
    try {
      const response = await adminAPI.cleanupCertificates();
      const { cleanedCount, invalidCertificates } = response.data;

      if (cleanedCount > 0) {
        message.success(`成功清理 ${cleanedCount} 个无效证书`);
        console.log('清理的证书详情:', invalidCertificates);
      } else {
        message.info('没有发现需要清理的无效证书');
      }
    } catch (error) {
      console.error('清理失败:', error);
      message.error('清理失败: ' + (error.response?.data?.message || error.message));
    }
  };

  const getStatusTag = (status) => {
    const statusMap = {
      active: { color: 'green', text: '进行中' },
      inactive: { color: 'red', text: '已结束' }
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns = [
    {
      title: '活动名称',
      dataIndex: 'title',
      key: 'title',
      render: (text) => <Text strong>{text}</Text>,
    },
    {
      title: '活动描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text) => text || '-',
    },
    {
      title: '活动时间',
      key: 'dateRange',
      render: (_, record) => (
        <div>
          <div>{record.start_date}</div>
          <div style={{ color: '#666' }}>至 {record.end_date}</div>
        </div>
      ),
    },
    {
      title: '允许类别',
      dataIndex: 'categories',
      key: 'categories',
      render: (categories) => {
        if (!categories || categories.length === 0) {
          return <Tag>全部类别</Tag>;
        }
        return (
          <div>
            {categories.slice(0, 2).map(catId => {
              const category = categories.find(cat => cat.id === catId);
              return category ? <Tag key={catId} size="small">{category.category_name}</Tag> : null;
            })}
            {categories.length > 2 && <Tag size="small">+{categories.length - 2}</Tag>}
          </div>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time) => new Date(time).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="link" 
            size="small"
            icon={<EditOutlined />} 
            onClick={() => {
              setEditingActivity(record);
              form.setFieldsValue({
                title: record.title,
                description: record.description,
                dateRange: [dayjs(record.start_date), dayjs(record.end_date)],
                categories: record.categories,
                status: record.status
              });
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除此审核活动吗？"
            description="⚠️ 删除活动将同时删除所有相关的证书提交记录，此操作不可恢复！"
            onConfirm={() => handleDelete(record.id)}
            okText="确定删除"
            cancelText="取消"
          >
            <Button 
              type="link" 
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={2}>
            <CalendarOutlined /> 审核活动管理
          </Title>
        </Col>
        <Col>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingActivity(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              创建审核活动
            </Button>
            <Popconfirm
              title="清理无效证书数据"
              description="这将删除所有没有关联审核活动的证书数据，确定继续吗？"
              onConfirm={handleCleanupCertificates}
              okText="确定清理"
              cancelText="取消"
            >
              <Button
                type="default"
                danger
                icon={<DeleteOutlined />}
              >
                清理无效数据
              </Button>
            </Popconfirm>
          </Space>
        </Col>
      </Row>

      <Card>
        <Table
          columns={columns}
          dataSource={activities}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingActivity ? '编辑审核活动' : '创建审核活动'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
          setEditingActivity(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="title"
            label="活动名称"
            rules={[{ required: true, message: '请输入活动名称' }]}
          >
            <Input placeholder="请输入活动名称，如：2025年春季学期素质测评" />
          </Form.Item>

          <Form.Item
            name="description"
            label="活动描述"
          >
            <TextArea
              rows={3}
              placeholder="请输入活动描述（可选）"
            />
          </Form.Item>

          <Form.Item
            name="dateRange"
            label="活动时间"
            rules={[{ required: true, message: '请选择活动时间' }]}
          >
            <RangePicker
              style={{ width: '100%' }}
              placeholder={['开始日期', '结束日期']}
            />
          </Form.Item>

          <Form.Item
            name="categories"
            label="允许提交的证书类别"
            extra="不选择表示允许所有类别"
          >
            <Select
              mode="multiple"
              placeholder="请选择允许的证书类别"
              allowClear
            >
              {categories.map(category => (
                <Option key={category.id} value={category.id}>
                  {category.category_name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="status"
            label="活动状态"
            initialValue="active"
          >
            <Select>
              <Option value="active">进行中</Option>
              <Option value="inactive">已结束</Option>
            </Select>
          </Form.Item>

          <Divider />

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingActivity ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AuditActivityManagement;
