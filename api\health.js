const { handleCors, handleSuccess } = require('./_middleware');

module.exports = async (req, res) => {
  // 处理 CORS
  if (handleCors(req, res)) return;

  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: '方法不允许' });
  }

  handleSuccess(res, {
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: 'Vercel + Supabase'
  }, 'Vercel Functions 运行正常');
};
