import React, { useState } from 'react';
import { 
  Card, 
  Button, 
  Upload, 
  message, 
  Space,
  Typography,
  Row,
  Col,
  Steps,
  Alert,
  Table,
  Progress,
  Divider,
  Select,
  Form
} from 'antd';
import { 
  UploadOutlined,
  DownloadOutlined,
  ImportOutlined,
  ExportOutlined,
  FileExcelOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { excelAPI, userAPI, auditActivityAPI } from '../../utils/api';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { Step } = Steps;

const ExcelImportExport = () => {
  const [importLoading, setImportLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [importResult, setImportResult] = useState(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [classes, setClasses] = useState([]);
  const [auditActivities, setAuditActivities] = useState([]);
  const [form] = Form.useForm();

  React.useEffect(() => {
    fetchClasses();
    fetchAuditActivities();
  }, []);

  const fetchClasses = async () => {
    try {
      const response = await userAPI.getClasses();
      setClasses(response.data.classes || []);
    } catch (error) {
      console.error('获取班级列表失败:', error);
    }
  };

  const fetchAuditActivities = async () => {
    try {
      const response = await auditActivityAPI.getActivities();
      console.log('获取审核活动响应:', response); // 调试日志

      // 兼容不同的数据结构
      const activities = response.data?.activities || response.data || [];
      console.log('解析的活动列表:', activities); // 调试日志

      setAuditActivities(activities);
    } catch (error) {
      console.error('获取审核活动列表失败:', error);
      setAuditActivities([]); // 确保在错误时设置为空数组
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      const response = await excelAPI.downloadStudentTemplate();
      const blob = new Blob([response], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = '学生信息导入模板.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('模板下载成功');
    } catch (error) {
      console.error('下载模板失败:', error);
      message.error('下载模板失败');
    }
  };

  const handleImportStudents = async (file) => {
    try {
      setImportLoading(true);
      setCurrentStep(1);
      
      const response = await excelAPI.importStudents(file);
      
      setImportResult(response.data);
      setCurrentStep(2);
      
      if (response.data.success_count > 0) {
        message.success(`成功导入 ${response.data.success_count} 个学生`);
      }
      
      if (response.data.error_count > 0) {
        message.warning(`${response.data.error_count} 个学生导入失败`);
      }
      
    } catch (error) {
      console.error('导入失败:', error);
      message.error('导入失败: ' + (error.response?.data?.message || error.message));
      setCurrentStep(0);
    } finally {
      setImportLoading(false);
    }
  };

  const handleExportScores = async (values) => {
    try {
      setExportLoading(true);

      const response = await excelAPI.exportScores({
        class_name: values.class_name,
        audit_activity_id: values.audit_activity_id,
        format: values.format || 'detailed'
      });

      const blob = new Blob([response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');

      // 根据审核活动生成文件名
      const activityName = values.audit_activity_id ?
        (Array.isArray(auditActivities) ? auditActivities.find(a => a.id === values.audit_activity_id)?.name : null) || '未知活动' :
        '全部活动';
      const className = values.class_name || '全部班级';

      link.href = url;
      link.download = `成绩报表_${activityName}_${className}_${new Date().toLocaleDateString()}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('成绩导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setExportLoading(false);
    }
  };

  const uploadProps = {
    beforeUpload: (file) => {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel';
      if (!isExcel) {
        message.error('只能上传Excel文件！');
        return false;
      }
      handleImportStudents(file);
      return false;
    },
    showUploadList: false,
  };

  const resetImport = () => {
    setCurrentStep(0);
    setImportResult(null);
  };

  const importSteps = [
    {
      title: '选择文件',
      description: '选择要导入的Excel文件',
      icon: <FileExcelOutlined />
    },
    {
      title: '处理中',
      description: '正在处理导入数据',
      icon: <ImportOutlined />
    },
    {
      title: '完成',
      description: '导入处理完成',
      icon: <CheckCircleOutlined />
    }
  ];

  const errorColumns = [
    {
      title: '行号',
      dataIndex: 'row',
      key: 'row',
      width: 80,
    },
    {
      title: '学号',
      dataIndex: 'student_id',
      key: 'student_id',
      width: 120,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 100,
    },
    {
      title: '班级',
      dataIndex: 'class_name',
      key: 'class_name',
    },
    {
      title: '错误原因',
      dataIndex: 'error',
      key: 'error',
      render: (error) => (
        <Text type="danger">{error}</Text>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>
        <ImportOutlined /> Excel导入导出
      </Title>

      <Row gutter={[24, 24]}>
        {/* 学生信息导入 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <ImportOutlined />
                学生信息导入
              </Space>
            }
            extra={
              <Button 
                type="link" 
                icon={<DownloadOutlined />}
                onClick={handleDownloadTemplate}
              >
                下载模板
              </Button>
            }
          >
            <Steps current={currentStep} size="small" style={{ marginBottom: 24 }}>
              {importSteps.map((step, index) => (
                <Step 
                  key={index}
                  title={step.title} 
                  description={step.description}
                  icon={step.icon}
                />
              ))}
            </Steps>

            {currentStep === 0 && (
              <div>
                <Alert
                  message="导入说明"
                  description={
                    <div>
                      <Paragraph>
                        1. 请先下载导入模板，按照模板格式填写学生信息
                      </Paragraph>
                      <Paragraph>
                        2. 必填字段：学号、姓名、班级
                      </Paragraph>
                      <Paragraph>
                        3. 学号必须唯一，不能重复
                      </Paragraph>
                      <Paragraph>
                        4. 支持.xlsx和.xls格式文件
                      </Paragraph>
                    </div>
                  }
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />

                <Upload {...uploadProps}>
                  <Button 
                    icon={<UploadOutlined />} 
                    size="large"
                    loading={importLoading}
                    block
                  >
                    选择Excel文件导入
                  </Button>
                </Upload>
              </div>
            )}

            {currentStep === 1 && (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Progress type="circle" percent={50} />
                <div style={{ marginTop: 16 }}>
                  <Text>正在处理导入数据，请稍候...</Text>
                </div>
              </div>
            )}

            {currentStep === 2 && importResult && (
              <div>
                <Alert
                  message="导入完成"
                  description={
                    <div>
                      <Text>成功导入：<Text strong style={{ color: '#52c41a' }}>{importResult.success_count}</Text> 个学生</Text>
                      <br />
                      <Text>导入失败：<Text strong style={{ color: '#ff4d4f' }}>{importResult.error_count}</Text> 个学生</Text>
                    </div>
                  }
                  type={importResult.error_count > 0 ? 'warning' : 'success'}
                  showIcon
                  style={{ marginBottom: 16 }}
                />

                {importResult.errors && importResult.errors.length > 0 && (
                  <div>
                    <Title level={5}>
                      <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                      导入失败记录
                    </Title>
                    <Table
                      size="small"
                      columns={errorColumns}
                      dataSource={importResult.errors}
                      rowKey={(record, index) => index}
                      pagination={false}
                      scroll={{ x: 600 }}
                    />
                  </div>
                )}

                <div style={{ textAlign: 'center', marginTop: 16 }}>
                  <Button onClick={resetImport}>
                    继续导入
                  </Button>
                </div>
              </div>
            )}
          </Card>
        </Col>

        {/* 成绩导出 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <ExportOutlined />
                成绩导出
              </Space>
            }
          >
            <Alert
              message="导出说明"
              description={
                <div>
                  <Paragraph>
                    1. 可以选择导出全部班级或指定班级的成绩
                  </Paragraph>
                  <Paragraph>
                    2. 导出内容包括学生基本信息、各项分值、最终成绩等
                  </Paragraph>
                  <Paragraph>
                    3. 支持详细版和简化版两种格式
                  </Paragraph>
                </div>
              }
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />

            <Form
              form={form}
              layout="vertical"
              onFinish={handleExportScores}
            >
              <Form.Item
                name="audit_activity_id"
                label="选择审核活动"
              >
                <Select
                  placeholder="选择审核活动（不选择则导出全部活动）"
                  allowClear
                >
                  {Array.isArray(auditActivities) ? auditActivities.map(activity => (
                    <Option key={activity.id} value={activity.id}>
                      {activity.name} ({activity.status === 'active' ? '进行中' : '已结束'})
                    </Option>
                  )) : []}
                </Select>
              </Form.Item>

              <Form.Item
                name="class_name"
                label="选择班级"
              >
                <Select
                  placeholder="选择班级（不选择则导出全部）"
                  allowClear
                >
                  {classes.map(cls => (
                    <Option key={cls.class_name} value={cls.class_name}>
                      {cls.class_name} ({cls.student_count}人)
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="format"
                label="导出格式"
                initialValue="detailed"
              >
                <Select>
                  <Option value="detailed">详细版（包含证书明细）</Option>
                  <Option value="summary">简化版（仅成绩汇总）</Option>
                </Select>
              </Form.Item>

              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit"
                  icon={<DownloadOutlined />}
                  loading={exportLoading}
                  size="large"
                  block
                >
                  导出成绩报表
                </Button>
              </Form.Item>
            </Form>

            <Divider />

            <div>
              <Title level={5}>快速导出</Title>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button 
                  block
                  onClick={() => handleExportScores({ format: 'summary' })}
                  loading={exportLoading}
                >
                  导出全部成绩（简化版）
                </Button>
                <Button 
                  block
                  onClick={() => handleExportScores({ format: 'detailed' })}
                  loading={exportLoading}
                >
                  导出全部成绩（详细版）
                </Button>
              </Space>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 使用说明 */}
      <Card title="使用说明" style={{ marginTop: 24 }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <Title level={4}>
              <ImportOutlined /> 学生信息导入
            </Title>
            <Paragraph>
              <Text strong>步骤：</Text>
            </Paragraph>
            <Paragraph>
              1. 点击"下载模板"获取标准Excel模板
            </Paragraph>
            <Paragraph>
              2. 在模板中填写学生信息（学号、姓名、班级）
            </Paragraph>
            <Paragraph>
              3. 保存Excel文件并上传导入
            </Paragraph>
            <Paragraph>
              4. 系统会自动验证数据并显示导入结果
            </Paragraph>
            <Paragraph>
              <Text type="warning">注意：学号必须唯一，重复的学号会导入失败</Text>
            </Paragraph>
          </Col>
          <Col xs={24} md={12}>
            <Title level={4}>
              <ExportOutlined /> 成绩导出
            </Title>
            <Paragraph>
              <Text strong>功能：</Text>
            </Paragraph>
            <Paragraph>
              1. 支持按班级筛选导出
            </Paragraph>
            <Paragraph>
              2. 提供详细版和简化版两种格式
            </Paragraph>
            <Paragraph>
              3. 详细版包含学生证书明细信息
            </Paragraph>
            <Paragraph>
              4. 简化版仅包含成绩汇总数据
            </Paragraph>
            <Paragraph>
              <Text type="success">导出的Excel文件可直接用于成绩统计和分析</Text>
            </Paragraph>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default ExcelImportExport;
