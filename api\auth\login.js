import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import {
  JWT_SECRET,
  db,
  handleCors,
  handleError,
  handleSuccess,
  logOperation
} from '../_middleware.js';

export default async function handler(req, res) {
  // 处理 CORS
  if (handleCors(req, res)) return;

  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: '方法不允许' });
  }

  try {
    const { student_id, password } = req.body;

    if (!student_id || !password) {
      return handleError(res, new Error('学号和密码不能为空'), 400);
    }

    // 查找用户
    const users = await db.getUsers({ username: student_id });
    let user = users[0];

    // 如果按用户名没找到，尝试按学号查找
    if (!user) {
      const usersByStudentId = await db.getUsers({ student_id });
      user = usersByStudentId[0];
    }

    if (!user) {
      return handleError(res, new Error('用户不存在'), 401);
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return handleError(res, new Error('密码错误'), 401);
    }

    // 生成 JWT 令牌
    const token = jwt.sign(
      { 
        id: user.id, 
        username: user.username, 
        role: user.role,
        permissions: user.permissions || []
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // 记录登录日志
    await logOperation(user.id, 'login', {
      login_method: 'password',
      user_agent: req.headers['user-agent'] || 'unknown'
    });

    // 返回用户信息和令牌
    const userInfo = {
      id: user.id,
      username: user.username,
      name: user.name,
      role: user.role,
      class_name: user.class_name,
      is_super_admin: user.is_super_admin || false,
      permissions: user.permissions || []
    };

    handleSuccess(res, {
      user: userInfo,
      token,
      expires_in: 24 * 60 * 60 // 24小时，单位秒
    }, '登录成功');

  } catch (error) {
    handleError(res, error);
  }
}
