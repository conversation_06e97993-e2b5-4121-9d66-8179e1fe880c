// Vercel Functions 中间件
const jwt = require('jsonwebtoken');
const { db } = require('./_supabase');

// JWT密钥
const JWT_SECRET = process.env.JWT_SECRET || 'demo_secret_key_' + Date.now();

// 令牌黑名单（用于登出和撤销）
const tokenBlacklist = new Set();

// 权限定义
const PERMISSIONS = {
  STUDENT_MANAGEMENT: 'student_management',
  CERTIFICATE_AUDIT: 'certificate_audit',
  GRADE_MANAGEMENT: 'grade_management',
  GRADE_EXPORT: 'grade_export',
  ACTIVITY_MANAGEMENT: 'activity_management',
  SYSTEM_SETTINGS: 'system_settings',
  ADMIN_MANAGEMENT: 'admin_management',
  LOG_MANAGEMENT: 'log_management'
};

// 身份验证中间件
function authenticateToken(req) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    throw new Error('未提供访问令牌');
  }

  if (tokenBlacklist.has(token)) {
    throw new Error('令牌已失效');
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return { user: decoded, token };
  } catch (error) {
    throw new Error('无效的访问令牌');
  }
}

// 教师权限检查
function requireTeacher(user) {
  if (user.role !== 'teacher') {
    throw new Error('需要教师权限');
  }
}

// 超级管理员权限检查
function requireSuperAdmin(user) {
  if (!user.is_super_admin) {
    throw new Error('需要超级管理员权限');
  }
}

// 权限检查
function requirePermission(permission) {
  return function(user) {
    if (!user.permissions || !user.permissions.includes(permission)) {
      throw new Error('权限不足');
    }
  };
}

// CORS 处理
function handleCors(req, res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return true;
  }
  return false;
}

// 错误处理
function handleError(res, error, statusCode = 500) {
  console.error('API Error:', error);
  res.status(statusCode).json({
    success: false,
    message: error.message || '服务器内部错误'
  });
}

// 成功响应
function handleSuccess(res, data = null, message = '操作成功') {
  res.status(200).json({
    success: true,
    message,
    data
  });
}

// 记录操作日志
async function logOperation(userId, action, details = {}) {
  try {
    const users = await db.getUsers({ id: userId });
    const user = users[0];

    const logData = {
      user_id: userId,
      username: user?.username || 'unknown',
      user_name: user?.name || 'unknown',
      action,
      details: typeof details === 'object' ? details : { message: details },
      ip_address: '0.0.0.0', // Vercel 中获取真实 IP 需要特殊处理
      user_agent: 'Vercel Function'
    };

    await db.createLog(logData);
  } catch (error) {
    console.error('记录日志失败:', error);
  }
}

module.exports = {
  JWT_SECRET,
  PERMISSIONS,
  db,
  tokenBlacklist,
  authenticateToken,
  requireTeacher,
  requireSuperAdmin,
  requirePermission,
  handleCors,
  handleError,
  handleSuccess,
  logOperation
};
