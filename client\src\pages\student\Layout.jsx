import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { Layout, Menu, Avatar, Dropdown, message, Spin } from 'antd';
import {
  HomeOutlined,
  FileTextOutlined,
  TrophyOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  KeyOutlined
} from '@ant-design/icons';
import { authAPI } from '../../utils/api';
import StudentHome from './Home';
import CertificateManagement from './CertificateManagement';
import MyScores from './MyScores';
import ChangePassword from '../ChangePassword';

const { Header, Sider, Content } = Layout;

const StudentLayout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    loadUserInfo();
  }, []);

  const loadUserInfo = async () => {
    try {
      const response = await authAPI.getCurrentUser();
      setUser(response.data.user);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      message.error('获取用户信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    message.success('退出登录成功');
    navigate('/login');
  };

  const menuItems = [
    {
      key: '/student/home',
      icon: <HomeOutlined />,
      label: '首页概览',
    },
    {
      key: '/student/certificates',
      icon: <FileTextOutlined />,
      label: '证书管理',
    },
    {
      key: '/student/scores',
      icon: <TrophyOutlined />,
      label: '我的成绩',
    },
    {
      key: '/student/change-password',
      icon: <KeyOutlined />,
      label: '修改密码',
    },
  ];

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
    },
    {
      key: 'change-password',
      icon: <KeyOutlined />,
      label: '修改密码',
      onClick: () => navigate('/student/change-password'),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  const handleMenuClick = ({ key }) => {
    navigate(key);
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        collapsible 
        collapsed={collapsed} 
        onCollapse={setCollapsed}
        theme="light"
        width={250}
      >
        <div style={{ 
          height: 64, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <h3 style={{ margin: 0, color: '#1890ff' }}>
            {collapsed ? '学生端' : '学生素质测评系统'}
          </h3>
        </div>
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ borderRight: 0 }}
        />
      </Sider>
      
      <Layout>
        <Header style={{
          background: '#fff',
          padding: '0 24px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid #f0f0f0',
          height: '64px'
        }}>
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            minWidth: 0,
            flex: 1
          }}>
            <div style={{
              fontSize: '16px',
              fontWeight: 500,
              margin: 0,
              lineHeight: '20px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}>
              欢迎，{user?.name}！
            </div>
            <div style={{
              color: '#666',
              fontSize: '12px',
              lineHeight: '16px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}>
              {user?.class_name} | {user?.student_id}
            </div>
          </div>

          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
          >
            <div style={{
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              marginLeft: '16px',
              flexShrink: 0
            }}>
              <Avatar icon={<UserOutlined />} style={{ marginRight: 8 }} />
              <span style={{
                maxWidth: '100px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {user?.name}
              </span>
            </div>
          </Dropdown>
        </Header>
        
        <Content style={{ margin: '24px', background: '#f0f2f5' }}>
          <Routes>
            <Route path="/" element={<Navigate to="/student/home" replace />} />
            <Route path="/home" element={<StudentHome />} />
            <Route path="/certificates" element={<CertificateManagement />} />
            <Route path="/scores" element={<MyScores />} />
            <Route path="/change-password" element={<ChangePassword />} />
            <Route path="*" element={<Navigate to="/student/home" replace />} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  );
};

export default StudentLayout;
