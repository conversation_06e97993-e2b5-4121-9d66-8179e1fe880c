import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { Layout, Menu, Avatar, Dropdown, message, Spin } from 'antd';
import '../../styles/layout.css';
import {
  DashboardOutlined,
  UserOutlined,
  FileTextOutlined,
  TrophyOutlined,
  ImportOutlined,
  SettingOutlined,
  LogoutOutlined,
  KeyOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  CalendarOutlined,
  HistoryOutlined,
  CrownOutlined
} from '@ant-design/icons';
import { authAPI } from '../../utils/api';
import TeacherHome from './Home';
import StudentManagement from './StudentManagement';
import CertificateAudit from './CertificateAudit';
import ScoreManagement from './ScoreManagement';
import ExcelImportExport from './ExcelImportExport';
import SystemSettings from './SystemSettings';
import AuditActivityManagement from './AuditActivityManagement';
import AdministratorManagement from './AdministratorManagement';
import OperationLogs from './OperationLogs';
import ChangePassword from '../ChangePassword';

const { Header, Sider, Content } = Layout;

const TeacherLayout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    loadUserInfo();
  }, []);

  const loadUserInfo = async () => {
    try {
      const response = await authAPI.getCurrentUser();
      setUser(response.data.user);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      message.error('获取用户信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    message.success('退出登录成功');
    navigate('/login');
  };

  // 权限检查函数
  const hasPermission = (permission) => {
    if (!user) return false;
    if (user.is_super_admin) return true;
    return user.permissions && user.permissions.includes(permission);
  };

  // 权限常量
  const PERMISSIONS = {
    CERTIFICATE_AUDIT: 'certificate_audit',
    STUDENT_MANAGEMENT: 'student_management',
    GRADE_EXPORT: 'grade_export',
    ACTIVITY_MANAGEMENT: 'activity_management',
    CATEGORY_MANAGEMENT: 'category_management',
    ADMIN_MANAGEMENT: 'admin_management',
    SYSTEM_SETTINGS: 'system_settings',
    DATA_CLEANUP: 'data_cleanup'
  };

  // 根据权限动态生成菜单项
  const getMenuItems = () => {
    const items = [
      {
        key: '/teacher/home',
        icon: <DashboardOutlined />,
        label: '工作台',
        show: true // 工作台所有人都能看到
      },
      {
        key: '/teacher/students',
        icon: <TeamOutlined />,
        label: '学生管理',
        show: hasPermission(PERMISSIONS.STUDENT_MANAGEMENT)
      },
      {
        key: '/teacher/certificates',
        icon: <CheckCircleOutlined />,
        label: '证书审核',
        show: hasPermission(PERMISSIONS.CERTIFICATE_AUDIT)
      },
      {
        key: '/teacher/audit-activities',
        icon: <CalendarOutlined />,
        label: '审核活动管理',
        show: hasPermission(PERMISSIONS.ACTIVITY_MANAGEMENT)
      },
      {
        key: '/teacher/scores',
        icon: <TrophyOutlined />,
        label: '成绩管理',
        show: hasPermission(PERMISSIONS.GRADE_EXPORT)
      },
      {
        key: '/teacher/excel',
        icon: <ImportOutlined />,
        label: 'Excel导入导出',
        show: hasPermission(PERMISSIONS.GRADE_EXPORT)
      },
      {
        key: '/teacher/administrators',
        icon: <CrownOutlined />,
        label: '管理员管理',
        show: hasPermission(PERMISSIONS.ADMIN_MANAGEMENT)
      },
      {
        key: '/teacher/operation-logs',
        icon: <HistoryOutlined />,
        label: '操作日志',
        show: hasPermission(PERMISSIONS.ADMIN_MANAGEMENT)
      },
      {
        key: '/teacher/settings',
        icon: <SettingOutlined />,
        label: '系统设置',
        show: hasPermission(PERMISSIONS.SYSTEM_SETTINGS)
      },
      {
        key: '/teacher/change-password',
        icon: <KeyOutlined />,
        label: '修改密码',
        show: true // 修改密码所有人都能看到
      },
    ];

    // 只返回有权限的菜单项
    return items.filter(item => item.show).map(({ show, ...item }) => item);
  };

  const menuItems = getMenuItems();

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
    },
    {
      key: 'change-password',
      icon: <KeyOutlined />,
      label: '修改密码',
      onClick: () => navigate('/teacher/change-password'),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  const handleMenuClick = ({ key }) => {
    navigate(key);
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        collapsible 
        collapsed={collapsed} 
        onCollapse={setCollapsed}
        theme="light"
        width={250}
      >
        <div style={{ 
          height: 64, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <h3 style={{ margin: 0, color: '#1890ff' }}>
            {collapsed ? '教师端' : '学生素质测评系统'}
          </h3>
        </div>
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ borderRight: 0 }}
        />
      </Sider>
      
      <Layout>
        <Header className="teacher-header">
          <div className="teacher-header-left">
            <span className="teacher-header-title">
              学生素质测评系统
            </span>
            <span className={`teacher-header-role-badge ${
              user?.is_super_admin ? 'super-admin' :
              user?.permissions?.length > 0 ? 'admin' : 'user'
            }`}>
              {user?.is_super_admin ? '超级管理员' :
               user?.permissions?.length > 0 ? '管理员' : '用户'}
            </span>
          </div>

          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
          >
            <div className="teacher-header-user-dropdown">
              <Avatar
                icon={<UserOutlined />}
                style={{
                  marginRight: 12,
                  backgroundColor: '#1890ff'
                }}
              />
              <div className="teacher-header-user-info">
                <div className="teacher-header-user-name">
                  {user?.name || '用户'}
                </div>
                <div className="teacher-header-user-id">
                  {user?.username || user?.student_id || 'admin'}
                </div>
              </div>
            </div>
          </Dropdown>
        </Header>
        
        <Content style={{ margin: '24px', background: '#f0f2f5' }}>
          <Routes>
            <Route path="/" element={<Navigate to="/teacher/home" replace />} />
            <Route path="/home" element={<TeacherHome />} />

            {/* 学生管理 */}
            {hasPermission(PERMISSIONS.STUDENT_MANAGEMENT) && (
              <Route path="/students" element={<StudentManagement />} />
            )}

            {/* 证书审核 */}
            {hasPermission(PERMISSIONS.CERTIFICATE_AUDIT) && (
              <Route path="/certificates" element={<CertificateAudit />} />
            )}

            {/* 审核活动管理 */}
            {hasPermission(PERMISSIONS.ACTIVITY_MANAGEMENT) && (
              <Route path="/audit-activities" element={<AuditActivityManagement />} />
            )}

            {/* 成绩管理 */}
            {hasPermission(PERMISSIONS.GRADE_EXPORT) && (
              <Route path="/scores" element={<ScoreManagement />} />
            )}

            {/* Excel导入导出 */}
            {hasPermission(PERMISSIONS.GRADE_EXPORT) && (
              <Route path="/excel" element={<ExcelImportExport />} />
            )}

            {/* 管理员管理 */}
            {hasPermission(PERMISSIONS.ADMIN_MANAGEMENT) && (
              <Route path="/administrators" element={<AdministratorManagement />} />
            )}

            {/* 操作日志 */}
            {hasPermission(PERMISSIONS.ADMIN_MANAGEMENT) && (
              <Route path="/operation-logs" element={<OperationLogs />} />
            )}

            {/* 系统设置 */}
            {hasPermission(PERMISSIONS.SYSTEM_SETTINGS) && (
              <Route path="/settings" element={<SystemSettings />} />
            )}

            {/* 修改密码 - 所有人都可以访问 */}
            <Route path="/change-password" element={<ChangePassword />} />

            <Route path="*" element={<Navigate to="/teacher/home" replace />} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  );
};

export default TeacherLayout;
