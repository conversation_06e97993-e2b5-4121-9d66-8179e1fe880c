# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
/client/dist/
/client/build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.lnk

# Temporary files
*.tmp
*.temp

# Database files (if using SQLite)
*.db
*.sqlite
*.sqlite3

# Uploads directory (contains user uploaded files)
/server/uploads/

# Data files (may contain sensitive information)
/server/data.json.backup

# Test files
test-*.js
*-test.js
*.test.js

# Documentation temp files
*_TEMP.md
*_BACKUP.md
BUG_FIX_*.md
SECURITY_CHANGES.md

.vercel
